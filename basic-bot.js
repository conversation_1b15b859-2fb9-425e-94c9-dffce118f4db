const { Client, GatewayIntentBits, Collection, EmbedBuilder, ActionRowBuilder, ButtonBuilder, ButtonStyle, StringSelectMenuBuilder } = require('discord.js');
const { createClient } = require('@supabase/supabase-js');
const { SAAS_BOTS_CONFIG } = require('./saas-bots-config');

// Configuração do Supabase
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_ANON_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

class BasicBot {
    constructor() {
        this.client = new Client({
            intents: [
                GatewayIntentBits.Guilds,
                GatewayIntentBits.GuildMessages,
                GatewayIntentBits.MessageContent,
                GatewayIntentBits.GuildMembers
            ]
        });

        this.config = SAAS_BOTS_CONFIG.basic;
        this.userConfigs = new Map(); // Cache das configurações dos usuários
        
        this.setupEventHandlers();
    }

    setupEventHandlers() {
        this.client.once('ready', () => {
            console.log(`✅ ${this.config.name} está online!`);
            this.client.user.setActivity('Sistema de Vendas | /config', { type: 'PLAYING' });
        });

        this.client.on('guildCreate', async (guild) => {
            console.log(`Bot adicionado ao servidor: ${guild.name} (${guild.id})`);
            await this.handleGuildJoin(guild);
        });

        this.client.on('interactionCreate', async (interaction) => {
            try {
                if (interaction.isChatInputCommand()) {
                    await this.handleSlashCommand(interaction);
                } else if (interaction.isButton()) {
                    await this.handleButtonInteraction(interaction);
                } else if (interaction.isStringSelectMenu()) {
                    await this.handleSelectMenuInteraction(interaction);
                }
            } catch (error) {
                console.error('Erro ao processar interação:', error);
                if (!interaction.replied && !interaction.deferred) {
                    await interaction.reply({
                        content: '❌ Ocorreu um erro ao processar sua solicitação.',
                        ephemeral: true
                    });
                }
            }
        });
    }

    async handleGuildJoin(guild) {
        // Verificar se existe configuração para este servidor
        const { data: userConfig } = await supabase
            .from('user_configurations')
            .select('*')
            .eq('guild_id', guild.id)
            .eq('license_plan', 'basic')
            .single();

        if (!userConfig) {
            // Enviar mensagem de boas-vindas para o dono do servidor
            const owner = await guild.fetchOwner();
            if (owner) {
                const welcomeEmbed = new EmbedBuilder()
                    .setTitle('🎉 Obrigado por adicionar o Nodex Bot Basic!')
                    .setDescription(`Olá **${owner.user.username}**!\n\nPara começar a usar o bot, você precisa:\n\n1️⃣ Ter uma licença ativa do plano Basic\n2️⃣ Ativar sua licença no dashboard\n3️⃣ Configurar o bot usando \`/config\`\n\n🔗 **Dashboard:** https://nodexsolutions.com.br/dashboard`)
                    .setColor(0x00ff00)
                    .setThumbnail(this.client.user.displayAvatarURL())
                    .setFooter({ text: 'Nodex Solutions - Sistema de Vendas' });

                try {
                    await owner.send({ embeds: [welcomeEmbed] });
                } catch (error) {
                    console.log('Não foi possível enviar DM para o dono do servidor');
                }
            }
        } else {
            // Atualizar informações do servidor na configuração
            await supabase
                .from('user_configurations')
                .update({
                    guild_name: guild.name,
                    last_activity: new Date().toISOString()
                })
                .eq('id', userConfig.id);

            this.userConfigs.set(guild.id, userConfig);
        }
    }

    async handleSlashCommand(interaction) {
        const { commandName } = interaction;

        switch (commandName) {
            case 'config':
                await this.handleConfigCommand(interaction);
                break;
            case 'help':
                await this.handleHelpCommand(interaction);
                break;
            case 'status':
                await this.handleStatusCommand(interaction);
                break;
            default:
                await interaction.reply({
                    content: '❌ Comando não reconhecido.',
                    ephemeral: true
                });
        }
    }

    async handleConfigCommand(interaction) {
        // Verificar se o usuário é o dono do servidor
        if (interaction.user.id !== interaction.guild.ownerId) {
            await interaction.reply({
                content: '❌ Apenas o dono do servidor pode usar este comando.',
                ephemeral: true
            });
            return;
        }

        // Buscar configuração do usuário
        const { data: userConfig } = await supabase
            .from('user_configurations')
            .select('*')
            .eq('discord_id', interaction.user.id)
            .eq('license_plan', 'basic')
            .single();

        if (!userConfig || !userConfig.license_validated) {
            const embed = new EmbedBuilder()
                .setTitle('❌ Licença Necessária')
                .setDescription('Você precisa de uma licença ativa do plano **Basic** para usar este bot.\n\n🔗 **Adquira sua licença:** https://nodexsolutions.com.br/dashboard')
                .setColor(0xff0000);

            await interaction.reply({ embeds: [embed], ephemeral: true });
            return;
        }

        // Atualizar guild_id se necessário
        if (userConfig.guild_id !== interaction.guild.id) {
            await supabase
                .from('user_configurations')
                .update({
                    guild_id: interaction.guild.id,
                    guild_name: interaction.guild.name,
                    last_activity: new Date().toISOString()
                })
                .eq('id', userConfig.id);

            userConfig.guild_id = interaction.guild.id;
            userConfig.guild_name = interaction.guild.name;
        }

        this.userConfigs.set(interaction.guild.id, userConfig);

        // Mostrar painel de configuração
        const embed = new EmbedBuilder()
            .setTitle('⚙️ Painel de Configuração - Nodex Bot Basic')
            .setDescription(`Bem-vindo ao painel de configuração!\n\n**Servidor:** ${interaction.guild.name}\n**Plano:** Basic\n\nEscolha uma opção abaixo para configurar:`)
            .setColor(0x00ff00)
            .addFields(
                { name: '🛒 Produtos', value: 'Configure seus produtos de venda', inline: true },
                { name: '🎫 Tickets', value: 'Sistema de suporte', inline: true },
                { name: '💳 Pagamentos', value: 'Configure PIX manual', inline: true },
                { name: '📊 Logs', value: 'Configure canais de log', inline: true },
                { name: '👥 Cargos', value: 'Configure cargos do sistema', inline: true },
                { name: '👋 Boas-vindas', value: 'Mensagens de boas-vindas', inline: true }
            );

        const row1 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('config_produtos')
                    .setLabel('Produtos')
                    .setEmoji('🛒')
                    .setStyle(ButtonStyle.Primary),
                new ButtonBuilder()
                    .setCustomId('config_tickets')
                    .setLabel('Tickets')
                    .setEmoji('🎫')
                    .setStyle(ButtonStyle.Primary),
                new ButtonBuilder()
                    .setCustomId('config_pagamentos')
                    .setLabel('Pagamentos')
                    .setEmoji('💳')
                    .setStyle(ButtonStyle.Primary)
            );

        const row2 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('config_logs')
                    .setLabel('Logs')
                    .setEmoji('📊')
                    .setStyle(ButtonStyle.Secondary),
                new ButtonBuilder()
                    .setCustomId('config_cargos')
                    .setLabel('Cargos')
                    .setEmoji('👥')
                    .setStyle(ButtonStyle.Secondary),
                new ButtonBuilder()
                    .setCustomId('config_boas_vindas')
                    .setLabel('Boas-vindas')
                    .setEmoji('👋')
                    .setStyle(ButtonStyle.Secondary)
            );

        await interaction.reply({
            embeds: [embed],
            components: [row1, row2],
            ephemeral: true
        });
    }

    async handleHelpCommand(interaction) {
        const embed = new EmbedBuilder()
            .setTitle('📚 Ajuda - Nodex Bot Basic')
            .setDescription('Comandos disponíveis:')
            .addFields(
                { name: '/config', value: 'Abrir painel de configuração', inline: false },
                { name: '/help', value: 'Mostrar esta mensagem de ajuda', inline: false },
                { name: '/status', value: 'Ver status do bot e licença', inline: false }
            )
            .setColor(0x00ff00)
            .setFooter({ text: 'Nodex Solutions - Plano Basic' });

        await interaction.reply({ embeds: [embed], ephemeral: true });
    }

    async handleStatusCommand(interaction) {
        const { data: userConfig } = await supabase
            .from('user_configurations')
            .select('*')
            .eq('discord_id', interaction.user.id)
            .single();

        const embed = new EmbedBuilder()
            .setTitle('📊 Status do Sistema')
            .addFields(
                { name: '🤖 Bot', value: 'Online', inline: true },
                { name: '📦 Plano', value: 'Basic', inline: true },
                { name: '✅ Status', value: userConfig?.license_validated ? 'Licença Ativa' : 'Licença Inválida', inline: true }
            )
            .setColor(userConfig?.license_validated ? 0x00ff00 : 0xff0000);

        await interaction.reply({ embeds: [embed], ephemeral: true });
    }

    async handleButtonInteraction(interaction) {
        // Implementar handlers dos botões de configuração
        const { customId } = interaction;

        switch (customId) {
            case 'config_produtos':
                await this.handleProductsConfig(interaction);
                break;
            case 'config_tickets':
                await this.handleTicketsConfig(interaction);
                break;
            case 'config_pagamentos':
                await this.handlePaymentsConfig(interaction);
                break;
            // Adicionar outros handlers conforme necessário
            default:
                await interaction.reply({
                    content: '🚧 Esta funcionalidade está em desenvolvimento.',
                    ephemeral: true
                });
        }
    }

    async handleSelectMenuInteraction(interaction) {
        // Implementar handlers dos select menus
        await interaction.reply({
            content: '🚧 Esta funcionalidade está em desenvolvimento.',
            ephemeral: true
        });
    }

    async handleProductsConfig(interaction) {
        const embed = new EmbedBuilder()
            .setTitle('🛒 Configuração de Produtos')
            .setDescription('Configure seus produtos de venda.\n\n**Limites do Plano Basic:**\n• Até 50 produtos\n• Até 5 categorias\n• PIX Manual apenas')
            .setColor(0x00ff00);

        await interaction.reply({ embeds: [embed], ephemeral: true });
    }

    async handleTicketsConfig(interaction) {
        const embed = new EmbedBuilder()
            .setTitle('🎫 Configuração de Tickets')
            .setDescription('Configure o sistema de suporte por tickets.')
            .setColor(0x00ff00);

        await interaction.reply({ embeds: [embed], ephemeral: true });
    }

    async handlePaymentsConfig(interaction) {
        const embed = new EmbedBuilder()
            .setTitle('💳 Configuração de Pagamentos')
            .setDescription('Configure o PIX manual para receber pagamentos.\n\n**Disponível no Plano Basic:**\n• PIX Manual')
            .setColor(0x00ff00);

        await interaction.reply({ embeds: [embed], ephemeral: true });
    }

    async start() {
        try {
            await this.client.login(this.config.token);
        } catch (error) {
            console.error('Erro ao iniciar o bot básico:', error);
        }
    }
}

module.exports = BasicBot;
