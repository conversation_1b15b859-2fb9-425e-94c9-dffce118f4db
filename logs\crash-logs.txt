[2025-06-19T03:12:57.666Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-19T03:13:02.825Z] GENERAL: Erro no botão back_to_config: row is not defined
Memory: N/A
Uptime: N/A
---
[2025-06-19T03:17:02.690Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-19T03:22:30.267Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-19T03:22:33.157Z] GENERAL: Erro no botão gerenciar_produtos: row1 is not defined
Memory: N/A
Uptime: N/A
---
[2025-06-19T03:24:59.507Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-19T03:27:08.028Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-19T03:27:38.849Z] UNHANDLEDREJECTION: The reply to this interaction has already been sent or deferred.
Stack: Error [InteractionAlreadyReplied]: The reply to this interaction has already been sent or deferred.
    at ModalSubmitInteraction.update (C:\Users\<USER>\Desktop\bot de vendas\node_modules\discord.js\src\structures\interfaces\InteractionResponses.js:342:46)
    at UserBot.handleGerenciarProdutos (C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:3808:27)
    at Timeout._onTimeout (C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:1481:32)
    at listOnTimeout (node:internal/timers:569:17)
    at process.processTimers (node:internal/timers:512:7)
Memory: 25MB
Uptime: 43s
---
[2025-06-19T03:32:03.632Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-19T03:32:38.362Z] GENERAL: Erro no botão back_to_auth: row1 is not defined
Memory: N/A
Uptime: N/A
---
[2025-06-19T03:33:09.276Z] UNHANDLEDREJECTION: The reply to this interaction has already been sent or deferred.
Stack: Error [InteractionAlreadyReplied]: The reply to this interaction has already been sent or deferred.
    at ModalSubmitInteraction.update (C:\Users\<USER>\Desktop\bot de vendas\node_modules\discord.js\src\structures\interfaces\InteractionResponses.js:342:46)
    at UserBot.handleGerenciarProdutos (C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:3833:27)
    at Timeout._onTimeout (C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:1506:32)
    at listOnTimeout (node:internal/timers:569:17)
    at process.processTimers (node:internal/timers:512:7)
Memory: 25MB
Uptime: 75s
---
[2025-06-19T19:05:59.804Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-19T19:32:05.552Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-19T19:38:10.712Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-19T20:00:36.563Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-19T20:01:02.434Z] UNHANDLEDREJECTION: The reply to this interaction has already been sent or deferred.
Stack: Error [InteractionAlreadyReplied]: The reply to this interaction has already been sent or deferred.
    at StringSelectMenuInteraction.update (C:\Users\<USER>\Desktop\bot de vendas\node_modules\discord.js\src\structures\interfaces\InteractionResponses.js:342:46)
    at UserBot.handleGerenciarProdutos (C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:5222:27)
    at Timeout._onTimeout (C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:764:32)
    at listOnTimeout (node:internal/timers:569:17)
    at process.processTimers (node:internal/timers:512:7)
Memory: 25MB
Uptime: 34s
---
[2025-06-19T20:05:17.809Z] GENERAL: Erro no servidor: listen EADDRINUSE: address already in use :::3003
Memory: N/A
Uptime: N/A
---
[2025-06-19T20:05:32.922Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-19T20:05:33.186Z] GENERAL: Erro no comando configurar: Unknown interaction
Memory: N/A
Uptime: N/A
---
[2025-06-19T20:05:36.535Z] GENERAL: Erro no botão config_loja: Unknown interaction
Memory: N/A
Uptime: N/A
---
[2025-06-19T20:05:38.034Z] GENERAL: Erro no botão gerenciar_produtos: Unknown interaction
Memory: N/A
Uptime: N/A
---
[2025-06-19T20:05:41.169Z] GENERAL: Erro no botão remover_produto: Unknown interaction
Memory: N/A
Uptime: N/A
---
[2025-06-19T20:05:43.779Z] WRAPPEDASYNC: Unknown interaction
Stack: DiscordAPIError[10062]: Unknown interaction
    at handleErrors (C:\Users\<USER>\Desktop\bot de vendas\node_modules\discord.js\node_modules\@discordjs\rest\dist\index.js:748:13)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async BurstHandler.runRequest (C:\Users\<USER>\Desktop\bot de vendas\node_modules\discord.js\node_modules\@discordjs\rest\dist\index.js:852:23)
    at async _REST.request (C:\Users\<USER>\Desktop\bot de vendas\node_modules\discord.js\node_modules\@discordjs\rest\dist\index.js:1293:22)
    at async StringSelectMenuInteraction.update (C:\Users\<USER>\Desktop\bot de vendas\node_modules\discord.js\src\structures\interfaces\InteractionResponses.js:361:22)
    at async UserBot.handleConfirmProductRemoval (C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:4113:9)
    at async UserBot.handleSelectMenu (C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:1064:13)
    at async C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:57:21
    at async Client.<anonymous> (C:\Users\<USER>\Desktop\bot de vendas\anticrash-system.js:186:24)
Memory: 26MB
Uptime: 315s
---
[2025-06-19T20:05:43.781Z] UNCAUGHTEXCEPTION: Unknown interaction
Stack: DiscordAPIError[10062]: Unknown interaction
    at handleErrors (C:\Users\<USER>\Desktop\bot de vendas\node_modules\discord.js\node_modules\@discordjs\rest\dist\index.js:748:13)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async BurstHandler.runRequest (C:\Users\<USER>\Desktop\bot de vendas\node_modules\discord.js\node_modules\@discordjs\rest\dist\index.js:852:23)
    at async _REST.request (C:\Users\<USER>\Desktop\bot de vendas\node_modules\discord.js\node_modules\@discordjs\rest\dist\index.js:1293:22)
    at async StringSelectMenuInteraction.update (C:\Users\<USER>\Desktop\bot de vendas\node_modules\discord.js\src\structures\interfaces\InteractionResponses.js:361:22)
    at async UserBot.handleConfirmProductRemoval (C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:4113:9)
    at async UserBot.handleSelectMenu (C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:1064:13)
    at async C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:57:21
    at async Client.<anonymous> (C:\Users\<USER>\Desktop\bot de vendas\anticrash-system.js:186:24)
Memory: 26MB
Uptime: 315s
---
[2025-06-19T20:05:45.388Z] GENERAL: Erro no botão confirm_remove_product_1750363257286: Unknown interaction
Memory: N/A
Uptime: N/A
---
[2025-06-19T20:05:46.291Z] GENERAL: Erro no botão confirm_remove_product_1750363257286: Unknown Message
Memory: N/A
Uptime: N/A
---
[2025-06-19T20:05:55.903Z] GENERAL: Erro no comando configurar: Unknown interaction
Memory: N/A
Uptime: N/A
---
[2025-06-19T20:05:57.498Z] GENERAL: Erro no botão config_loja: Unknown interaction
Memory: N/A
Uptime: N/A
---
[2025-06-19T20:05:58.908Z] GENERAL: Erro no botão gerenciar_produtos: Unknown interaction
Memory: N/A
Uptime: N/A
---
[2025-06-19T20:06:00.816Z] GENERAL: Erro no botão criar_produto: Unknown interaction
Memory: N/A
Uptime: N/A
---
[2025-06-19T20:06:09.857Z] WRAPPEDASYNC: Unknown interaction
Stack: DiscordAPIError[10062]: Unknown interaction
    at handleErrors (C:\Users\<USER>\Desktop\bot de vendas\node_modules\discord.js\node_modules\@discordjs\rest\dist\index.js:748:13)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async BurstHandler.runRequest (C:\Users\<USER>\Desktop\bot de vendas\node_modules\discord.js\node_modules\@discordjs\rest\dist\index.js:852:23)
    at async _REST.request (C:\Users\<USER>\Desktop\bot de vendas\node_modules\discord.js\node_modules\@discordjs\rest\dist\index.js:1293:22)
    at async ModalSubmitInteraction.deferUpdate (C:\Users\<USER>\Desktop\bot de vendas\node_modules\discord.js\src\structures\interfaces\InteractionResponses.js:312:22)
    at async UserBot.handleModalSubmit (C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:1841:13)
    at async C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:61:21
    at async Client.<anonymous> (C:\Users\<USER>\Desktop\bot de vendas\anticrash-system.js:186:24)
Memory: 25MB
Uptime: 54s
---
[2025-06-19T20:06:09.859Z] UNCAUGHTEXCEPTION: Unknown interaction
Stack: DiscordAPIError[10062]: Unknown interaction
    at handleErrors (C:\Users\<USER>\Desktop\bot de vendas\node_modules\discord.js\node_modules\@discordjs\rest\dist\index.js:748:13)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async BurstHandler.runRequest (C:\Users\<USER>\Desktop\bot de vendas\node_modules\discord.js\node_modules\@discordjs\rest\dist\index.js:852:23)
    at async _REST.request (C:\Users\<USER>\Desktop\bot de vendas\node_modules\discord.js\node_modules\@discordjs\rest\dist\index.js:1293:22)
    at async ModalSubmitInteraction.deferUpdate (C:\Users\<USER>\Desktop\bot de vendas\node_modules\discord.js\src\structures\interfaces\InteractionResponses.js:312:22)
    at async UserBot.handleModalSubmit (C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:1841:13)
    at async C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:61:21
    at async Client.<anonymous> (C:\Users\<USER>\Desktop\bot de vendas\anticrash-system.js:186:24)
Memory: 25MB
Uptime: 54s
---
[2025-06-19T20:06:13.263Z] WRAPPEDASYNC: Interaction has already been acknowledged.
Stack: DiscordAPIError[40060]: Interaction has already been acknowledged.
    at handleErrors (C:\Users\<USER>\Desktop\bot de vendas\node_modules\discord.js\node_modules\@discordjs\rest\dist\index.js:748:13)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async BurstHandler.runRequest (C:\Users\<USER>\Desktop\bot de vendas\node_modules\discord.js\node_modules\@discordjs\rest\dist\index.js:852:23)
    at async _REST.request (C:\Users\<USER>\Desktop\bot de vendas\node_modules\discord.js\node_modules\@discordjs\rest\dist\index.js:1293:22)
    at async StringSelectMenuInteraction.update (C:\Users\<USER>\Desktop\bot de vendas\node_modules\discord.js\src\structures\interfaces\InteractionResponses.js:361:22)
    at async UserBot.handleSelectMenu (C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:772:17)
    at async C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:57:21
    at async Client.<anonymous> (C:\Users\<USER>\Desktop\bot de vendas\anticrash-system.js:186:24)
Memory: 27MB
Uptime: 345s
---
[2025-06-19T20:06:13.264Z] UNCAUGHTEXCEPTION: Interaction has already been acknowledged.
Stack: DiscordAPIError[40060]: Interaction has already been acknowledged.
    at handleErrors (C:\Users\<USER>\Desktop\bot de vendas\node_modules\discord.js\node_modules\@discordjs\rest\dist\index.js:748:13)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async BurstHandler.runRequest (C:\Users\<USER>\Desktop\bot de vendas\node_modules\discord.js\node_modules\@discordjs\rest\dist\index.js:852:23)
    at async _REST.request (C:\Users\<USER>\Desktop\bot de vendas\node_modules\discord.js\node_modules\@discordjs\rest\dist\index.js:1293:22)
    at async StringSelectMenuInteraction.update (C:\Users\<USER>\Desktop\bot de vendas\node_modules\discord.js\src\structures\interfaces\InteractionResponses.js:361:22)
    at async UserBot.handleSelectMenu (C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:772:17)
    at async C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:57:21
    at async Client.<anonymous> (C:\Users\<USER>\Desktop\bot de vendas\anticrash-system.js:186:24)
Memory: 27MB
Uptime: 345s
---
[2025-06-19T20:06:14.263Z] UNHANDLEDREJECTION: The reply to this interaction has already been sent or deferred.
Stack: Error [InteractionAlreadyReplied]: The reply to this interaction has already been sent or deferred.
    at StringSelectMenuInteraction.update (C:\Users\<USER>\Desktop\bot de vendas\node_modules\discord.js\src\structures\interfaces\InteractionResponses.js:342:46)
    at UserBot.handleGerenciarProdutos (C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:5497:27)
    at Timeout._onTimeout (C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:680:32)
    at listOnTimeout (node:internal/timers:569:17)
    at process.processTimers (node:internal/timers:512:7)
Memory: 26MB
Uptime: 58s
---
[2025-06-19T20:06:23.873Z] GENERAL: Erro no botão config_product_description_1750363572566: Unknown interaction
Memory: N/A
Uptime: N/A
---
[2025-06-19T20:06:26.166Z] WRAPPEDASYNC: Interaction has already been acknowledged.
Stack: DiscordAPIError[40060]: Interaction has already been acknowledged.
    at handleErrors (C:\Users\<USER>\Desktop\bot de vendas\node_modules\discord.js\node_modules\@discordjs\rest\dist\index.js:748:13)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async BurstHandler.runRequest (C:\Users\<USER>\Desktop\bot de vendas\node_modules\discord.js\node_modules\@discordjs\rest\dist\index.js:852:23)
    at async _REST.request (C:\Users\<USER>\Desktop\bot de vendas\node_modules\discord.js\node_modules\@discordjs\rest\dist\index.js:1293:22)
    at async ModalSubmitInteraction.reply (C:\Users\<USER>\Desktop\bot de vendas\node_modules\discord.js\src\structures\interfaces\InteractionResponses.js:200:22)
    at async UserBot.handleUpdateProductConfigDetails (C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:3820:13)
    at async UserBot.handleModalSubmit (C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:1670:13)
    at async C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:61:21
    at async Client.<anonymous> (C:\Users\<USER>\Desktop\bot de vendas\anticrash-system.js:186:24)
Memory: 27MB
Uptime: 358s
---
[2025-06-19T20:06:26.168Z] UNCAUGHTEXCEPTION: Interaction has already been acknowledged.
Stack: DiscordAPIError[40060]: Interaction has already been acknowledged.
    at handleErrors (C:\Users\<USER>\Desktop\bot de vendas\node_modules\discord.js\node_modules\@discordjs\rest\dist\index.js:748:13)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async BurstHandler.runRequest (C:\Users\<USER>\Desktop\bot de vendas\node_modules\discord.js\node_modules\@discordjs\rest\dist\index.js:852:23)
    at async _REST.request (C:\Users\<USER>\Desktop\bot de vendas\node_modules\discord.js\node_modules\@discordjs\rest\dist\index.js:1293:22)
    at async ModalSubmitInteraction.reply (C:\Users\<USER>\Desktop\bot de vendas\node_modules\discord.js\src\structures\interfaces\InteractionResponses.js:200:22)
    at async UserBot.handleUpdateProductConfigDetails (C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:3820:13)
    at async UserBot.handleModalSubmit (C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:1670:13)
    at async C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:61:21
    at async Client.<anonymous> (C:\Users\<USER>\Desktop\bot de vendas\anticrash-system.js:186:24)
Memory: 27MB
Uptime: 358s
---
[2025-06-19T20:06:30.931Z] GENERAL: Erro no botão config_product_price_1750363572566: Unknown interaction
Memory: N/A
Uptime: N/A
---
[2025-06-19T20:06:33.180Z] WRAPPEDASYNC: Interaction has already been acknowledged.
Stack: DiscordAPIError[40060]: Interaction has already been acknowledged.
    at handleErrors (C:\Users\<USER>\Desktop\bot de vendas\node_modules\discord.js\node_modules\@discordjs\rest\dist\index.js:748:13)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async BurstHandler.runRequest (C:\Users\<USER>\Desktop\bot de vendas\node_modules\discord.js\node_modules\@discordjs\rest\dist\index.js:852:23)
    at async _REST.request (C:\Users\<USER>\Desktop\bot de vendas\node_modules\discord.js\node_modules\@discordjs\rest\dist\index.js:1293:22)
    at async ModalSubmitInteraction.reply (C:\Users\<USER>\Desktop\bot de vendas\node_modules\discord.js\src\structures\interfaces\InteractionResponses.js:200:22)
    at async UserBot.handleUpdateProductConfigDetails (C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:3820:13)
    at async UserBot.handleModalSubmit (C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:1678:13)
    at async C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:61:21
    at async Client.<anonymous> (C:\Users\<USER>\Desktop\bot de vendas\anticrash-system.js:186:24)
Memory: 26MB
Uptime: 365s
---
[2025-06-19T20:06:33.182Z] UNCAUGHTEXCEPTION: Interaction has already been acknowledged.
Stack: DiscordAPIError[40060]: Interaction has already been acknowledged.
    at handleErrors (C:\Users\<USER>\Desktop\bot de vendas\node_modules\discord.js\node_modules\@discordjs\rest\dist\index.js:748:13)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async BurstHandler.runRequest (C:\Users\<USER>\Desktop\bot de vendas\node_modules\discord.js\node_modules\@discordjs\rest\dist\index.js:852:23)
    at async _REST.request (C:\Users\<USER>\Desktop\bot de vendas\node_modules\discord.js\node_modules\@discordjs\rest\dist\index.js:1293:22)
    at async ModalSubmitInteraction.reply (C:\Users\<USER>\Desktop\bot de vendas\node_modules\discord.js\src\structures\interfaces\InteractionResponses.js:200:22)
    at async UserBot.handleUpdateProductConfigDetails (C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:3820:13)
    at async UserBot.handleModalSubmit (C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:1678:13)
    at async C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:61:21
    at async Client.<anonymous> (C:\Users\<USER>\Desktop\bot de vendas\anticrash-system.js:186:24)
Memory: 26MB
Uptime: 365s
---
[2025-06-19T20:06:35.393Z] GENERAL: Erro no botão config_product_save_1750363572566: Unknown interaction
Memory: N/A
Uptime: N/A
---
[2025-06-19T20:09:20.606Z] GENERAL: Erro no servidor: listen EADDRINUSE: address already in use :::3003
Memory: N/A
Uptime: N/A
---
[2025-06-19T20:09:33.765Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-19T20:09:34.041Z] GENERAL: Erro no comando configurar: Unknown interaction
Memory: N/A
Uptime: N/A
---
[2025-06-19T20:09:35.853Z] GENERAL: Erro no botão config_loja: Unknown interaction
Memory: N/A
Uptime: N/A
---
[2025-06-19T20:09:37.454Z] GENERAL: Erro no botão gerenciar_produtos: Unknown interaction
Memory: N/A
Uptime: N/A
---
[2025-06-19T20:09:50.081Z] GENERAL: Erro no botão remover_produto: Interaction has already been acknowledged.
Memory: N/A
Uptime: N/A
---
[2025-06-19T20:09:52.518Z] WRAPPEDASYNC: Unknown interaction
Stack: DiscordAPIError[10062]: Unknown interaction
    at handleErrors (C:\Users\<USER>\Desktop\bot de vendas\node_modules\discord.js\node_modules\@discordjs\rest\dist\index.js:748:13)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async BurstHandler.runRequest (C:\Users\<USER>\Desktop\bot de vendas\node_modules\discord.js\node_modules\@discordjs\rest\dist\index.js:852:23)
    at async _REST.request (C:\Users\<USER>\Desktop\bot de vendas\node_modules\discord.js\node_modules\@discordjs\rest\dist\index.js:1293:22)
    at async StringSelectMenuInteraction.update (C:\Users\<USER>\Desktop\bot de vendas\node_modules\discord.js\src\structures\interfaces\InteractionResponses.js:361:22)
    at async UserBot.handleConfirmProductRemoval (C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:4471:9)
    at async UserBot.handleSelectMenu (C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:1065:13)
    at async C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:57:21
    at async Client.<anonymous> (C:\Users\<USER>\Desktop\bot de vendas\anticrash-system.js:186:24)
Memory: 25MB
Uptime: 34s
---
[2025-06-19T20:09:52.519Z] UNCAUGHTEXCEPTION: Unknown interaction
Stack: DiscordAPIError[10062]: Unknown interaction
    at handleErrors (C:\Users\<USER>\Desktop\bot de vendas\node_modules\discord.js\node_modules\@discordjs\rest\dist\index.js:748:13)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async BurstHandler.runRequest (C:\Users\<USER>\Desktop\bot de vendas\node_modules\discord.js\node_modules\@discordjs\rest\dist\index.js:852:23)
    at async _REST.request (C:\Users\<USER>\Desktop\bot de vendas\node_modules\discord.js\node_modules\@discordjs\rest\dist\index.js:1293:22)
    at async StringSelectMenuInteraction.update (C:\Users\<USER>\Desktop\bot de vendas\node_modules\discord.js\src\structures\interfaces\InteractionResponses.js:361:22)
    at async UserBot.handleConfirmProductRemoval (C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:4471:9)
    at async UserBot.handleSelectMenu (C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:1065:13)
    at async C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:57:21
    at async Client.<anonymous> (C:\Users\<USER>\Desktop\bot de vendas\anticrash-system.js:186:24)
Memory: 25MB
Uptime: 34s
---
[2025-06-19T20:09:53.858Z] GENERAL: Erro no botão confirm_remove_product_1750363257286: Unknown interaction
Memory: N/A
Uptime: N/A
---
[2025-06-19T20:09:57.708Z] UNHANDLEDREJECTION: The reply to this interaction has already been sent or deferred.
Stack: Error [InteractionAlreadyReplied]: The reply to this interaction has already been sent or deferred.
    at ButtonInteraction.update (C:\Users\<USER>\Desktop\bot de vendas\node_modules\discord.js\src\structures\interfaces\InteractionResponses.js:342:46)
    at UserBot.handleGerenciarProdutos (C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:5222:27)
    at Timeout._onTimeout (C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:4199:28)
    at listOnTimeout (node:internal/timers:569:17)
    at process.processTimers (node:internal/timers:512:7)
Memory: 27MB
Uptime: 569s
---
[2025-06-19T20:10:05.895Z] GENERAL: Erro no comando configurar: Unknown interaction
Memory: N/A
Uptime: N/A
---
[2025-06-19T20:10:07.313Z] GENERAL: Erro no botão config_loja: Unknown interaction
Memory: N/A
Uptime: N/A
---
[2025-06-19T20:10:09.361Z] GENERAL: Erro no botão gerenciar_produtos: Unknown interaction
Memory: N/A
Uptime: N/A
---
[2025-06-19T20:10:13.467Z] GENERAL: Erro no botão remover_produto: Unknown interaction
Memory: N/A
Uptime: N/A
---
[2025-06-19T20:10:16.775Z] WRAPPEDASYNC: Unknown interaction
Stack: DiscordAPIError[10062]: Unknown interaction
    at handleErrors (C:\Users\<USER>\Desktop\bot de vendas\node_modules\discord.js\node_modules\@discordjs\rest\dist\index.js:748:13)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async BurstHandler.runRequest (C:\Users\<USER>\Desktop\bot de vendas\node_modules\discord.js\node_modules\@discordjs\rest\dist\index.js:852:23)
    at async _REST.request (C:\Users\<USER>\Desktop\bot de vendas\node_modules\discord.js\node_modules\@discordjs\rest\dist\index.js:1293:22)
    at async StringSelectMenuInteraction.update (C:\Users\<USER>\Desktop\bot de vendas\node_modules\discord.js\src\structures\interfaces\InteractionResponses.js:361:22)
    at async UserBot.handleConfirmProductRemoval (C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:4113:9)
    at async UserBot.handleSelectMenu (C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:1064:13)
    at async C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:57:21
    at async Client.<anonymous> (C:\Users\<USER>\Desktop\bot de vendas\anticrash-system.js:186:24)
Memory: 27MB
Uptime: 588s
---
[2025-06-19T20:10:16.777Z] UNCAUGHTEXCEPTION: Unknown interaction
Stack: DiscordAPIError[10062]: Unknown interaction
    at handleErrors (C:\Users\<USER>\Desktop\bot de vendas\node_modules\discord.js\node_modules\@discordjs\rest\dist\index.js:748:13)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async BurstHandler.runRequest (C:\Users\<USER>\Desktop\bot de vendas\node_modules\discord.js\node_modules\@discordjs\rest\dist\index.js:852:23)
    at async _REST.request (C:\Users\<USER>\Desktop\bot de vendas\node_modules\discord.js\node_modules\@discordjs\rest\dist\index.js:1293:22)
    at async StringSelectMenuInteraction.update (C:\Users\<USER>\Desktop\bot de vendas\node_modules\discord.js\src\structures\interfaces\InteractionResponses.js:361:22)
    at async UserBot.handleConfirmProductRemoval (C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:4113:9)
    at async UserBot.handleSelectMenu (C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:1064:13)
    at async C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:57:21
    at async Client.<anonymous> (C:\Users\<USER>\Desktop\bot de vendas\anticrash-system.js:186:24)
Memory: 27MB
Uptime: 588s
---
[2025-06-19T20:10:18.053Z] GENERAL: Erro no botão confirm_remove_product_1750363572566: Unknown interaction
Memory: N/A
Uptime: N/A
---
[2025-06-19T20:10:18.830Z] GENERAL: Erro no botão confirm_remove_product_1750363572566: Unknown Message
Memory: N/A
Uptime: N/A
---
[2025-06-19T20:10:23.548Z] GENERAL: Erro no comando configurar: Unknown interaction
Memory: N/A
Uptime: N/A
---
[2025-06-19T20:10:24.992Z] GENERAL: Erro no botão config_loja: Unknown interaction
Memory: N/A
Uptime: N/A
---
[2025-06-19T20:10:26.437Z] GENERAL: Erro no botão gerenciar_produtos: Unknown interaction
Memory: N/A
Uptime: N/A
---
[2025-06-19T20:53:41.517Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-19T21:01:50.738Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-19T21:07:46.419Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-19T21:09:39.059Z] GENERAL: Erro no botão confirm_delete_product_1750366446375: Unknown Message
Memory: N/A
Uptime: N/A
---
[2025-06-19T21:09:54.062Z] UNHANDLEDREJECTION: The reply to this interaction has already been sent or deferred.
Stack: Error [InteractionAlreadyReplied]: The reply to this interaction has already been sent or deferred.
    at ButtonInteraction.update (C:\Users\<USER>\Desktop\bot de vendas\node_modules\discord.js\src\structures\interfaces\InteractionResponses.js:342:46)
    at UserBot.handleGerenciarProdutos (C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:5746:27)
    at Timeout._onTimeout (C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:3075:28)
    at listOnTimeout (node:internal/timers:569:17)
    at process.processTimers (node:internal/timers:512:7)
Memory: 26MB
Uptime: 149s
---
[2025-06-19T21:26:55.401Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-19T21:35:10.754Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-19T21:41:16.007Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-19T22:05:17.375Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-19T22:08:38.708Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-19T22:11:20.985Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-19T22:24:50.737Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-19T22:28:49.682Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-19T22:29:56.461Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-19T22:34:08.467Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-19T22:50:29.179Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-19T22:51:04.486Z] GENERAL: Erro no botão confirm_remove_product_1750367416230: Unknown Message
Memory: N/A
Uptime: N/A
---
[2025-06-19T22:56:15.761Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-19T22:56:28.406Z] GENERAL: Erro no botão cart_remove_product_1750373484495_1202721227999944777: Unknown interaction
Memory: N/A
Uptime: N/A
---
[2025-06-19T22:57:23.063Z] GENERAL: Erro no botão cart_cancel_1750373484495_1195685295568470056: Unknown interaction
Memory: N/A
Uptime: N/A
---
[2025-06-19T23:00:46.887Z] GENERAL: Erro no botão cart_cancel_1750373484495_1202721227999944777: Unknown interaction
Memory: N/A
Uptime: N/A
---
[2025-06-19T23:27:52.032Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-19T23:28:28.254Z] GENERAL: Erro no botão buy_product_1750373484495: Unknown interaction
Memory: N/A
Uptime: N/A
---
[2025-06-19T23:30:20.559Z] GENERAL: Erro no botão checkout_dm_1750373484495_1202721227999944777: Unknown interaction
Memory: N/A
Uptime: N/A
---
[2025-06-19T23:38:44.336Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-19T23:39:02.734Z] GENERAL: Erro no botão cart_cancel_1750373484495_1202721227999944777: Unknown interaction
Memory: N/A
Uptime: N/A
---
[2025-06-19T23:41:59.110Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-19T23:43:09.761Z] GENERAL: Erro no botão cart_cancel_1750373484495_1202721227999944777: Unknown interaction
Memory: N/A
Uptime: N/A
---
[2025-06-19T23:47:00.121Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-19T23:49:22.370Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-19T23:51:43.921Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-19T23:51:54.272Z] WARNING: buffer.File is an experimental feature and might change at any time
Memory: N/A
Uptime: N/A
---
[2025-06-20T00:07:12.295Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-20T00:16:17.780Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-20T00:24:52.096Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-20T00:24:52.467Z] GENERAL: Erro no botão buy_product_1750373484495: Unknown interaction
Memory: N/A
Uptime: N/A
---
[2025-06-20T00:25:42.516Z] WARNING: buffer.File is an experimental feature and might change at any time
Memory: N/A
Uptime: N/A
---
[2025-06-20T00:34:38.566Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-20T00:34:56.808Z] WARNING: buffer.File is an experimental feature and might change at any time
Memory: N/A
Uptime: N/A
---
[2025-06-20T00:43:58.995Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-20T00:44:11.310Z] GENERAL: Erro no botão cart_cancel_1750373484495_1202721227999944777: Unknown interaction
Memory: N/A
Uptime: N/A
---
[2025-06-20T00:44:47.589Z] WARNING: buffer.File is an experimental feature and might change at any time
Memory: N/A
Uptime: N/A
---
[2025-06-20T00:58:02.150Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-20T00:58:02.479Z] GENERAL: Erro no botão buy_product_1750373484495: Unknown interaction
Memory: N/A
Uptime: N/A
---
[2025-06-20T00:58:15.046Z] WARNING: buffer.File is an experimental feature and might change at any time
Memory: N/A
Uptime: N/A
---
[2025-06-20T01:00:23.317Z] GENERAL: Erro no botão cart_cancel_1750373484495_1202721227999944777: Unknown interaction
Memory: N/A
Uptime: N/A
---
[2025-06-20T01:06:22.957Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-20T01:06:52.185Z] GENERAL: Erro no botão cart_cancel_1750373484495_1202721227999944777: Unknown interaction
Memory: N/A
Uptime: N/A
---
[2025-06-22T17:00:16.037Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-22T17:00:33.562Z] GENERAL: Erro no botão cart_cancel_1750373484495_1202721227999944777: Unknown interaction
Memory: N/A
Uptime: N/A
---
[2025-06-22T17:08:27.545Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-22T17:08:32.547Z] GENERAL: Erro no botão buy_product_1750373484495: database.getGuild is not a function
Memory: N/A
Uptime: N/A
---
[2025-06-22T17:08:40.203Z] GENERAL: Erro no botão buy_product_1750373484495: database.getGuild is not a function
Memory: N/A
Uptime: N/A
---
[2025-06-22T17:12:07.246Z] GENERAL: Erro no botão buy_product_1750373484495: Assignment to constant variable.
Memory: N/A
Uptime: N/A
---
[2025-06-22T17:12:07.251Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-22T17:12:29.068Z] GENERAL: Erro no botão buy_product_1750373484495: Assignment to constant variable.
Memory: N/A
Uptime: N/A
---
[2025-06-22T21:03:02.185Z] GENERAL: Erro no botão buy_product_1750373484495: Assignment to constant variable.
Memory: N/A
Uptime: N/A
---
[2025-06-22T21:03:02.191Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-22T21:08:12.232Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-22T21:09:16.487Z] WARNING: buffer.File is an experimental feature and might change at any time
Memory: N/A
Uptime: N/A
---
[2025-06-22T21:14:09.983Z] WARNING: buffer.File is an experimental feature and might change at any time
Memory: N/A
Uptime: N/A
---
[2025-06-22T21:16:31.580Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-22T22:00:39.313Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-22T22:02:56.959Z] GENERAL: Erro no botão cart_cancel_1750373484495_1202721227999944777: Unknown interaction
Memory: N/A
Uptime: N/A
---
[2025-06-22T22:04:49.877Z] WARNING: buffer.File is an experimental feature and might change at any time
Memory: N/A
Uptime: N/A
---
[2025-06-22T22:10:54.241Z] WARNING: buffer.File is an experimental feature and might change at any time
Memory: N/A
Uptime: N/A
---
[2025-06-22T22:13:17.072Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-22T22:14:43.232Z] WARNING: buffer.File is an experimental feature and might change at any time
Memory: N/A
Uptime: N/A
---
[2025-06-22T22:20:09.826Z] WARNING: buffer.File is an experimental feature and might change at any time
Memory: N/A
Uptime: N/A
---
[2025-06-23T12:54:05.485Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-23T13:04:25.651Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-23T13:08:47.598Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-23T13:22:32.437Z] GENERAL: Erro no comando configurar: (intermediate value).setCustomId(...).setLabel(...).setemoji is not a function
Memory: N/A
Uptime: N/A
---
[2025-06-23T13:22:32.442Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-23T13:33:47.022Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-23T13:38:41.297Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-23T13:59:12.103Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-23T14:27:47.722Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-23T14:43:19.960Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-23T14:43:33.300Z] UNHANDLEDREJECTION: The reply to this interaction has already been sent or deferred.
Stack: Error [InteractionAlreadyReplied]: The reply to this interaction has already been sent or deferred.
    at ButtonInteraction.update (C:\Users\<USER>\Desktop\bot de vendas\node_modules\discord.js\src\structures\interfaces\InteractionResponses.js:342:46)
    at UserBot.handleConfigurarBoasVindas (C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:11135:27)
    at Timeout._onTimeout (C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:11348:24)
    at listOnTimeout (node:internal/timers:569:17)
    at process.processTimers (node:internal/timers:512:7)
Memory: 26MB
Uptime: 23s
---
[2025-06-23T15:02:21.985Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-23T15:03:21.908Z] UNHANDLEDREJECTION: The reply to this interaction has already been sent or deferred.
Stack: Error [InteractionAlreadyReplied]: The reply to this interaction has already been sent or deferred.
    at ButtonInteraction.update (C:\Users\<USER>\Desktop\bot de vendas\node_modules\discord.js\src\structures\interfaces\InteractionResponses.js:342:46)
    at UserBot.handleConfigurarBoasVindas (C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:11128:27)
    at Timeout._onTimeout (C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:11361:24)
    at listOnTimeout (node:internal/timers:569:17)
    at process.processTimers (node:internal/timers:512:7)
Memory: 25MB
Uptime: 68s
---
[2025-06-23T15:03:24.839Z] UNHANDLEDREJECTION: The reply to this interaction has already been sent or deferred.
Stack: Error [InteractionAlreadyReplied]: The reply to this interaction has already been sent or deferred.
    at ButtonInteraction.update (C:\Users\<USER>\Desktop\bot de vendas\node_modules\discord.js\src\structures\interfaces\InteractionResponses.js:342:46)
    at UserBot.handleConfigurarBoasVindas (C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:11128:27)
    at Timeout._onTimeout (C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:11361:24)
    at listOnTimeout (node:internal/timers:569:17)
    at process.processTimers (node:internal/timers:512:7)
Memory: 26MB
Uptime: 71s
---
[2025-06-23T15:03:27.043Z] UNHANDLEDREJECTION: The reply to this interaction has already been sent or deferred.
Stack: Error [InteractionAlreadyReplied]: The reply to this interaction has already been sent or deferred.
    at ButtonInteraction.update (C:\Users\<USER>\Desktop\bot de vendas\node_modules\discord.js\src\structures\interfaces\InteractionResponses.js:342:46)
    at UserBot.handleConfigurarBoasVindas (C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:11128:27)
    at Timeout._onTimeout (C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:11361:24)
    at listOnTimeout (node:internal/timers:569:17)
    at process.processTimers (node:internal/timers:512:7)
Memory: 26MB
Uptime: 73s
---
[2025-06-23T15:14:21.671Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-23T15:27:54.556Z] UNHANDLEDREJECTION: The reply to this interaction has already been sent or deferred.
Stack: Error [InteractionAlreadyReplied]: The reply to this interaction has already been sent or deferred.
    at ModalSubmitInteraction.update (C:\Users\<USER>\Desktop\bot de vendas\node_modules\discord.js\src\structures\interfaces\InteractionResponses.js:342:46)
    at UserBot.handleConfigurarCanalBoasVindas (C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:11454:27)
    at Timeout._onTimeout (C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:2643:28)
    at listOnTimeout (node:internal/timers:569:17)
    at process.processTimers (node:internal/timers:512:7)
Memory: 26MB
Uptime: 255s
---
[2025-06-23T15:28:01.044Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-23T15:28:09.963Z] GENERAL: Erro no botão preview_mensagem_boas_vindas: Cannot read properties of undefined (reading 'replace')
Memory: N/A
Uptime: N/A
---
[2025-06-23T15:34:25.339Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-23T15:34:37.482Z] UNHANDLEDREJECTION: The reply to this interaction has already been sent or deferred.
Stack: Error [InteractionAlreadyReplied]: The reply to this interaction has already been sent or deferred.
    at ModalSubmitInteraction.update (C:\Users\<USER>\Desktop\bot de vendas\node_modules\discord.js\src\structures\interfaces\InteractionResponses.js:342:46)
    at UserBot.handleConfigurarMensagemBoasVindas (C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:11498:27)
    at Timeout._onTimeout (C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:2536:32)
    at listOnTimeout (node:internal/timers:569:17)
    at process.processTimers (node:internal/timers:512:7)
Memory: 26MB
Uptime: 44s
---
[2025-06-23T15:42:14.110Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-23T15:45:33.921Z] GENERAL: Erro no botão confirm_delete_product_1750693512071: Unknown Message
Memory: N/A
Uptime: N/A
---
[2025-06-23T18:36:52.015Z] GENERAL: Erro no botão config_definicoes: Invalid Form Body
data.components[0].components[1].emoji.name[BUTTON_COMPONENT_INVALID_EMOJI]: Invalid emoji
Memory: N/A
Uptime: N/A
---
[2025-06-23T18:36:52.288Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-23T18:44:45.838Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-23T20:25:12.300Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-23T20:28:12.833Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-23T20:41:34.630Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-23T20:42:14.515Z] UNHANDLEDREJECTION: The reply to this interaction has already been sent or deferred.
Stack: Error [InteractionAlreadyReplied]: The reply to this interaction has already been sent or deferred.
    at ModalSubmitInteraction.update (C:\Users\<USER>\Desktop\bot de vendas\node_modules\discord.js\src\structures\interfaces\InteractionResponses.js:342:46)
    at UserBot.handleConfigurarTicket (C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:11041:27)
    at Timeout._onTimeout (C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:12391:28)
    at listOnTimeout (node:internal/timers:569:17)
    at process.processTimers (node:internal/timers:512:7)
Memory: 26MB
Uptime: 78s
---
[2025-06-23T20:42:32.446Z] UNHANDLEDREJECTION: The reply to this interaction has already been sent or deferred.
Stack: Error [InteractionAlreadyReplied]: The reply to this interaction has already been sent or deferred.
    at ModalSubmitInteraction.update (C:\Users\<USER>\Desktop\bot de vendas\node_modules\discord.js\src\structures\interfaces\InteractionResponses.js:342:46)
    at UserBot.handleConfigurarTicket (C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:11041:27)
    at Timeout._onTimeout (C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:12454:24)
    at listOnTimeout (node:internal/timers:569:17)
    at process.processTimers (node:internal/timers:512:7)
Memory: 26MB
Uptime: 96s
---
[2025-06-23T20:51:27.421Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-23T20:58:16.303Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-23T21:27:26.352Z] UNHANDLEDREJECTION: The reply to this interaction has already been sent or deferred.
Stack: Error [InteractionAlreadyReplied]: The reply to this interaction has already been sent or deferred.
    at ButtonInteraction.update (C:\Users\<USER>\Desktop\bot de vendas\node_modules\discord.js\src\structures\interfaces\InteractionResponses.js:342:46)
    at UserBot.handleConfigurarTicket (C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:11065:27)
    at Timeout._onTimeout (C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:12534:28)
    at listOnTimeout (node:internal/timers:569:17)
    at process.processTimers (node:internal/timers:512:7)
Memory: 26MB
Uptime: 265s
---
[2025-06-23T23:24:15.141Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-23T23:24:23.377Z] GENERAL: Erro no botão edit_product_settings_1750373484495: Cannot read properties of undefined (reading 'find')
Memory: N/A
Uptime: N/A
---
[2025-06-24T00:17:06.550Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-24T00:34:59.383Z] GENERAL: Erro no botão cart_cancel_1750373484495_1195685295568470056: Unknown interaction
Memory: N/A
Uptime: N/A
---
[2025-06-24T00:56:59.977Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-24T01:01:56.704Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-24T01:14:44.472Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-24T02:01:56.630Z] UNHANDLEDREJECTION: The reply to this interaction has already been sent or deferred.
Stack: Error [InteractionAlreadyReplied]: The reply to this interaction has already been sent or deferred.
    at ButtonInteraction.update (C:\Users\<USER>\Desktop\bot de vendas\node_modules\discord.js\src\structures\interfaces\InteractionResponses.js:342:46)
    at UserBot.handleConfigCanais (C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:9618:27)
    at Timeout._onTimeout (C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:14391:28)
    at listOnTimeout (node:internal/timers:569:17)
    at process.processTimers (node:internal/timers:512:7)
Memory: 26MB
Uptime: 59s
---
[2025-06-24T02:02:06.126Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-24T02:34:40.651Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-24T02:42:59.682Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-24T02:47:23.750Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-24T02:50:25.032Z] UNHANDLEDREJECTION: The reply to this interaction has already been sent or deferred.
Stack: Error [InteractionAlreadyReplied]: The reply to this interaction has already been sent or deferred.
    at ButtonInteraction.update (C:\Users\<USER>\Desktop\bot de vendas\node_modules\discord.js\src\structures\interfaces\InteractionResponses.js:342:46)
    at UserBot.handleConfigPixManual (C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:15572:27)
    at Timeout._onTimeout (C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:15643:24)
    at listOnTimeout (node:internal/timers:569:17)
    at process.processTimers (node:internal/timers:512:7)
Memory: 26MB
Uptime: 196s
---
[2025-06-24T02:58:11.384Z] UNHANDLEDREJECTION: The reply to this interaction has already been sent or deferred.
Stack: Error [InteractionAlreadyReplied]: The reply to this interaction has already been sent or deferred.
    at ButtonInteraction.update (C:\Users\<USER>\Desktop\bot de vendas\node_modules\discord.js\src\structures\interfaces\InteractionResponses.js:342:46)
    at UserBot.handleConfigMercadoPago (C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:8884:27)
    at Timeout._onTimeout (C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:15504:24)
    at listOnTimeout (node:internal/timers:569:17)
    at process.processTimers (node:internal/timers:512:7)
Memory: 26MB
Uptime: 35s
---
[2025-06-24T02:58:43.663Z] UNHANDLEDREJECTION: The reply to this interaction has already been sent or deferred.
Stack: Error [InteractionAlreadyReplied]: The reply to this interaction has already been sent or deferred.
    at ButtonInteraction.update (C:\Users\<USER>\Desktop\bot de vendas\node_modules\discord.js\src\structures\interfaces\InteractionResponses.js:342:46)
    at UserBot.handleConfigMercadoPago (C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:8884:27)
    at Timeout._onTimeout (C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:15468:24)
    at listOnTimeout (node:internal/timers:569:17)
    at process.processTimers (node:internal/timers:512:7)
Memory: 26MB
Uptime: 67s
---
[2025-06-24T03:01:40.072Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-24T03:08:50.541Z] GENERAL: Erro no botão config_mercado_pago_payment: row3 is not defined
Memory: N/A
Uptime: N/A
---
[2025-06-24T03:08:50.545Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-24T13:19:08.643Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-24T13:42:07.862Z] WRAPPEDASYNC: Unknown interaction
Stack: DiscordAPIError[10062]: Unknown interaction
    at handleErrors (C:\Users\<USER>\Desktop\bot de vendas\node_modules\discord.js\node_modules\@discordjs\rest\dist\index.js:748:13)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async BurstHandler.runRequest (C:\Users\<USER>\Desktop\bot de vendas\node_modules\discord.js\node_modules\@discordjs\rest\dist\index.js:852:23)
    at async _REST.request (C:\Users\<USER>\Desktop\bot de vendas\node_modules\discord.js\node_modules\@discordjs\rest\dist\index.js:1293:22)
    at async StringSelectMenuInteraction.update (C:\Users\<USER>\Desktop\bot de vendas\node_modules\discord.js\src\structures\interfaces\InteractionResponses.js:361:22)
    at async UserBot.handleCriarCanaisNaCategoria (C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:9925:13)
    at async UserBot.handleSelectMenu (C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:1217:13)
    at async C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:63:21
    at async Client.<anonymous> (C:\Users\<USER>\Desktop\bot de vendas\anticrash-system.js:186:24)
Memory: 26MB
Uptime: 255s
---
[2025-06-24T13:42:07.865Z] UNCAUGHTEXCEPTION: Unknown interaction
Stack: DiscordAPIError[10062]: Unknown interaction
    at handleErrors (C:\Users\<USER>\Desktop\bot de vendas\node_modules\discord.js\node_modules\@discordjs\rest\dist\index.js:748:13)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async BurstHandler.runRequest (C:\Users\<USER>\Desktop\bot de vendas\node_modules\discord.js\node_modules\@discordjs\rest\dist\index.js:852:23)
    at async _REST.request (C:\Users\<USER>\Desktop\bot de vendas\node_modules\discord.js\node_modules\@discordjs\rest\dist\index.js:1293:22)
    at async StringSelectMenuInteraction.update (C:\Users\<USER>\Desktop\bot de vendas\node_modules\discord.js\src\structures\interfaces\InteractionResponses.js:361:22)
    at async UserBot.handleCriarCanaisNaCategoria (C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:9925:13)
    at async UserBot.handleSelectMenu (C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:1217:13)
    at async C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:63:21
    at async Client.<anonymous> (C:\Users\<USER>\Desktop\bot de vendas\anticrash-system.js:186:24)
Memory: 26MB
Uptime: 255s
---
[2025-06-24T13:46:19.562Z] UNHANDLEDREJECTION: The reply to this interaction has already been sent or deferred.
Stack: Error [InteractionAlreadyReplied]: The reply to this interaction has already been sent or deferred.
    at ButtonInteraction.update (C:\Users\<USER>\Desktop\bot de vendas\node_modules\discord.js\src\structures\interfaces\InteractionResponses.js:342:46)
    at UserBot.handleConfigCanais (C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:9713:27)
    at Timeout._onTimeout (C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:14617:24)
    at listOnTimeout (node:internal/timers:569:17)
    at process.processTimers (node:internal/timers:512:7)
Memory: 26MB
Uptime: 36s
---
[2025-06-24T13:46:24.896Z] UNHANDLEDREJECTION: The reply to this interaction has already been sent or deferred.
Stack: Error [InteractionAlreadyReplied]: The reply to this interaction has already been sent or deferred.
    at ButtonInteraction.update (C:\Users\<USER>\Desktop\bot de vendas\node_modules\discord.js\src\structures\interfaces\InteractionResponses.js:342:46)
    at UserBot.handleConfigCanais (C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:9713:27)
    at Timeout._onTimeout (C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:14617:24)
    at listOnTimeout (node:internal/timers:569:17)
    at process.processTimers (node:internal/timers:512:7)
Memory: 26MB
Uptime: 41s
---
[2025-06-24T13:58:36.889Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-24T14:00:27.172Z] GENERAL: Erro no botão buy_product_1750373484495: Unknown interaction
Memory: N/A
Uptime: N/A
---
[2025-06-24T14:00:35.649Z] GENERAL: Erro no botão cart_cancel_1750373484495_1202721227999944777: Unknown interaction
Memory: N/A
Uptime: N/A
---
[2025-06-24T14:56:34.044Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-24T15:04:35.749Z] GENERAL: Erro no botão back_to_config_pagamentos: this.handleDefinicoes is not a function
Memory: N/A
Uptime: N/A
---
[2025-06-24T15:04:35.753Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-24T15:06:17.213Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-24T15:09:57.155Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-24T15:14:35.908Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-24T17:12:53.346Z] UNCAUGHTEXCEPTION: Neither apiKey nor config.authenticator provided
Stack: Error: Neither apiKey nor config.authenticator provided
    at Stripe._setAuthenticator (C:\Users\<USER>\Desktop\bot de vendas\node_modules\stripe\cjs\stripe.core.js:155:23)
    at new Stripe (C:\Users\<USER>\Desktop\bot de vendas\node_modules\stripe\cjs\stripe.core.js:93:14)
    at Stripe (C:\Users\<USER>\Desktop\bot de vendas\node_modules\stripe\cjs\stripe.core.js:49:20)
    at Object.<anonymous> (C:\Users\<USER>\Desktop\bot de vendas\server.js:9:33)
    at Module._compile (node:internal/modules/cjs/loader:1356:14)
    at Module._extensions..js (node:internal/modules/cjs/loader:1414:10)
    at Module.load (node:internal/modules/cjs/loader:1197:32)
    at Module._load (node:internal/modules/cjs/loader:1013:12)
    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:128:12)
    at node:internal/main/run_main_module:28:49
Memory: 32MB
Uptime: 1s
---
[2025-06-24T18:04:36.836Z] GENERAL: Erro no servidor: listen EADDRINUSE: address already in use :::3003
Memory: N/A
Uptime: N/A
---
[2025-06-24T18:11:19.506Z] GENERAL: Erro na autenticação: Request failed with status code 400
Memory: N/A
Uptime: N/A
---
[2025-06-24T18:11:47.913Z] GENERAL: Erro na autenticação: Request failed with status code 400
Memory: N/A
Uptime: N/A
---
[2025-06-24T18:46:03.653Z] UNCAUGHTEXCEPTION: Configurações do Supabase não encontradas no .env
Stack: Error: Configurações do Supabase não encontradas no .env
    at new SupabaseDatabase (C:\Users\<USER>\Desktop\bot de vendas\supabase-database.js:10:19)
    at Object.<anonymous> (C:\Users\<USER>\Desktop\bot de vendas\server.js:6:18)
    at Module._compile (node:internal/modules/cjs/loader:1356:14)
    at Module._extensions..js (node:internal/modules/cjs/loader:1414:10)
    at Module.load (node:internal/modules/cjs/loader:1197:32)
    at Module._load (node:internal/modules/cjs/loader:1013:12)
    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:128:12)
    at node:internal/main/run_main_module:28:49
Memory: 34MB
Uptime: 1s
---
[2025-06-24T19:40:16.001Z] UNCAUGHTEXCEPTION: await is only valid in async functions and the top level bodies of modules
Stack: C:\Users\<USER>\Desktop\bot de vendas\auth-middleware.js:158
        await database.loadFromSupabase();
        ^^^^^

SyntaxError: await is only valid in async functions and the top level bodies of modules
    at internalCompileFunction (node:internal/vm:73:18)
    at wrapSafe (node:internal/modules/cjs/loader:1274:20)
    at Module._compile (node:internal/modules/cjs/loader:1320:27)
    at Module._extensions..js (node:internal/modules/cjs/loader:1414:10)
    at Module.load (node:internal/modules/cjs/loader:1197:32)
    at Module._load (node:internal/modules/cjs/loader:1013:12)
    at Module.require (node:internal/modules/cjs/loader:1225:19)
    at require (node:internal/modules/helpers:177:18)
    at Object.<anonymous> (C:\Users\<USER>\Desktop\bot de vendas\server.js:11:70)
    at Module._compile (node:internal/modules/cjs/loader:1356:14)
Memory: 34MB
Uptime: 1s
---
[2025-06-24T19:48:12.999Z] GENERAL: Erro ao criar sessão de checkout: Invalid URL: An explicit scheme (such as https) must be provided.
Memory: N/A
Uptime: N/A
---
[2025-06-24T20:22:04.073Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-24T20:23:59.087Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-24T20:25:22.912Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-24T20:29:53.737Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-24T20:31:13.648Z] GENERAL: Erro ao criar sessão de checkout: Invalid URL: An explicit scheme (such as https) must be provided.
Memory: N/A
Uptime: N/A
---
[2025-06-24T20:33:44.671Z] GENERAL: Erro ao criar sessão de checkout: Invalid URL: An explicit scheme (such as https) must be provided.
Memory: N/A
Uptime: N/A
---
[2025-06-24T20:34:19.531Z] GENERAL: Erro ao criar sessão de checkout: Invalid URL: An explicit scheme (such as https) must be provided.
Memory: N/A
Uptime: N/A
---
[2025-06-24T20:36:53.289Z] GENERAL: Erro no servidor: listen EADDRINUSE: address already in use :::3003
Memory: N/A
Uptime: N/A
---
[2025-06-24T20:37:42.970Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-26T01:32:30.463Z] GENERAL: Erro ao executar ação do bot: Cannot access 'userData' before initialization
Memory: N/A
Uptime: N/A
---
[2025-06-26T02:59:27.720Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-26T02:59:33.000Z] GENERAL: Erro no comando autenticacao: Cannot read properties of undefined (reading 'licenseKey')
Memory: N/A
Uptime: N/A
---
[2025-06-26T03:12:03.598Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-26T03:16:15.997Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-26T03:25:15.860Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-26T03:27:53.197Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-26T13:34:16.979Z] GENERAL: Falha ao iniciar o bot principal
Memory: N/A
Uptime: N/A
---
[2025-06-26T13:34:16.982Z] WRAPPEDASYNC: Falha ao iniciar o bot principal
Stack: Error: Falha ao iniciar o bot principal
    at C:\Users\<USER>\Desktop\bot de vendas\server.js:1790:15
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async C:\Users\<USER>\Desktop\bot de vendas\anticrash-system.js:186:24
    at async C:\Users\<USER>\Desktop\bot de vendas\server.js:1798:9
    at async C:\Users\<USER>\Desktop\bot de vendas\anticrash-system.js:186:24
Memory: 29MB
Uptime: 11s
---
[2025-06-26T13:34:16.985Z] GENERAL: Erro ao inicializar sistema: Falha ao iniciar o bot principal
Memory: N/A
Uptime: N/A
---
[2025-06-26T13:34:16.985Z] WRAPPEDASYNC: Falha ao iniciar o bot principal
Stack: Error: Falha ao iniciar o bot principal
    at C:\Users\<USER>\Desktop\bot de vendas\server.js:1790:15
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async C:\Users\<USER>\Desktop\bot de vendas\anticrash-system.js:186:24
    at async C:\Users\<USER>\Desktop\bot de vendas\server.js:1798:9
    at async C:\Users\<USER>\Desktop\bot de vendas\anticrash-system.js:186:24
Memory: 29MB
Uptime: 11s
---
[2025-06-26T13:34:16.987Z] UNHANDLEDREJECTION: Falha ao iniciar o bot principal
Stack: Error: Falha ao iniciar o bot principal
    at C:\Users\<USER>\Desktop\bot de vendas\server.js:1790:15
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async C:\Users\<USER>\Desktop\bot de vendas\anticrash-system.js:186:24
    at async C:\Users\<USER>\Desktop\bot de vendas\server.js:1798:9
    at async C:\Users\<USER>\Desktop\bot de vendas\anticrash-system.js:186:24
Memory: 29MB
Uptime: 11s
---
[2025-06-26T13:34:44.985Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-26T13:44:09.229Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-26T14:09:01.938Z] UNCAUGHTEXCEPTION: BotManager is not a constructor
Stack: TypeError: BotManager is not a constructor
    at Object.<anonymous> (C:\Users\<USER>\Desktop\bot de vendas\server.js:10:20)
    at Module._compile (node:internal/modules/cjs/loader:1356:14)
    at Module._extensions..js (node:internal/modules/cjs/loader:1414:10)
    at Module.load (node:internal/modules/cjs/loader:1197:32)
    at Module._load (node:internal/modules/cjs/loader:1013:12)
    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:128:12)
    at node:internal/main/run_main_module:28:49
Memory: 34MB
Uptime: 1s
---
[2025-06-26T14:23:33.411Z] GENERAL: Erro ao executar ação do bot: database is not defined
Memory: N/A
Uptime: N/A
---
[2025-06-26T14:24:27.486Z] GENERAL: Erro no servidor: listen EADDRINUSE: address already in use :::3003
Memory: N/A
Uptime: N/A
---
[2025-06-26T14:37:18.386Z] GENERAL: Erro no comando autenticacao: database is not defined
Memory: N/A
Uptime: N/A
---
[2025-06-26T14:37:18.392Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-26T14:42:56.957Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-26T14:44:40.322Z] UNHANDLEDREJECTION: The reply to this interaction has already been sent or deferred.
Stack: Error [InteractionAlreadyReplied]: The reply to this interaction has already been sent or deferred.
    at ModalSubmitInteraction.update (C:\Users\<USER>\Desktop\bot de vendas\node_modules\discord.js\src\structures\interfaces\InteractionResponses.js:342:46)
    at UserBot.handleGerenciarProdutos (C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:9332:27)
    at Timeout._onTimeout (C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:3057:32)
    at listOnTimeout (node:internal/timers:569:17)
    at process.processTimers (node:internal/timers:512:7)
Memory: 32MB
Uptime: 119s
---
[2025-06-26T14:58:56.284Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-26T15:15:44.830Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-26T15:16:46.100Z] UNHANDLEDREJECTION: The reply to this interaction has already been sent or deferred.
Stack: Error [InteractionAlreadyReplied]: The reply to this interaction has already been sent or deferred.
    at ModalSubmitInteraction.update (C:\Users\<USER>\Desktop\bot de vendas\node_modules\discord.js\src\structures\interfaces\InteractionResponses.js:342:46)
    at UserBot.handleConfigCanais (C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:10115:27)
    at Timeout._onTimeout (C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:15197:24)
    at listOnTimeout (node:internal/timers:569:17)
    at process.processTimers (node:internal/timers:512:7)
Memory: 34MB
Uptime: 92s
---
[2025-06-26T15:21:37.197Z] WARNING: buffer.File is an experimental feature and might change at any time
Memory: N/A
Uptime: N/A
---
[2025-06-26T16:46:15.306Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-26T17:18:16.323Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-26T17:19:57.007Z] GENERAL: Erro no botão confirm_remove_product_1750956442306: Unknown Message
Memory: N/A
Uptime: N/A
---
[2025-06-26T17:24:50.799Z] GENERAL: Erro no servidor: listen EADDRINUSE: address already in use :::3003
Memory: N/A
Uptime: N/A
---
[2025-06-26T17:33:21.035Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-26T17:38:55.254Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-26T17:40:37.506Z] GENERAL: Erro no botão clear_mercado_pago_credentials: The reply to this interaction has already been sent or deferred.
Memory: N/A
Uptime: N/A
---
[2025-06-26T17:57:10.634Z] GENERAL: Erro no servidor: listen EADDRINUSE: address already in use :::3003
Memory: N/A
Uptime: N/A
---
[2025-06-26T18:00:34.730Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-26T18:06:19.927Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-26T18:07:27.688Z] GENERAL: Erro no servidor: listen EADDRINUSE: address already in use :::3003
Memory: N/A
Uptime: N/A
---
[2025-06-26T18:08:24.768Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-26T18:20:40.025Z] GENERAL: Erro no servidor: listen EADDRINUSE: address already in use :::3003
Memory: N/A
Uptime: N/A
---
[2025-06-26T18:35:18.846Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-26T21:30:21.462Z] GENERAL: Erro no comando configurar: (intermediate value).setTitle(...).setDescription(...).setColor(...).setimage is not a function
Memory: N/A
Uptime: N/A
---
[2025-06-26T21:30:21.468Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-26T21:33:35.372Z] GENERAL: Erro no comando configurar: (intermediate value).setTitle(...).setDescription(...).setColor(...).setBanner is not a function
Memory: N/A
Uptime: N/A
---
[2025-06-26T21:33:35.378Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-26T21:35:08.844Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-26T21:41:16.219Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-26T21:50:00.308Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-26T21:56:13.412Z] GENERAL: Erro no servidor: listen EADDRINUSE: address already in use :::3003
Memory: N/A
Uptime: N/A
---
[2025-06-26T22:06:31.879Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-26T22:06:32.107Z] GENERAL: Erro no comando configurar: Invalid Form Body
data.components[2].components[1].emoji.id[BUTTON_COMPONENT_INVALID_EMOJI]: Invalid emoji
Memory: N/A
Uptime: N/A
---
[2025-06-26T22:07:35.899Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-26T22:15:08.025Z] GENERAL: Erro no servidor: listen EADDRINUSE: address already in use :::3003
Memory: N/A
Uptime: N/A
---
[2025-06-26T22:21:56.645Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-26T22:25:27.489Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-27T00:51:41.499Z] UNHANDLEDREJECTION: The reply to this interaction has already been sent or deferred.
Stack: Error [InteractionAlreadyReplied]: The reply to this interaction has already been sent or deferred.
    at ModalSubmitInteraction.update (C:\Users\<USER>\Desktop\bot de vendas\node_modules\discord.js\src\structures\interfaces\InteractionResponses.js:342:46)
    at UserBot.handleConfigurarBoasVindas (C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:12485:27)
    at Timeout._onTimeout (C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:3598:28)
    at listOnTimeout (node:internal/timers:569:17)
    at process.processTimers (node:internal/timers:512:7)
Memory: 33MB
Uptime: 8788s
---
[2025-06-27T00:52:24.778Z] UNHANDLEDREJECTION: The reply to this interaction has already been sent or deferred.
Stack: Error [InteractionAlreadyReplied]: The reply to this interaction has already been sent or deferred.
    at ModalSubmitInteraction.update (C:\Users\<USER>\Desktop\bot de vendas\node_modules\discord.js\src\structures\interfaces\InteractionResponses.js:342:46)
    at UserBot.handleConfigurarMensagemBoasVindas (C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:12807:27)
    at Timeout._onTimeout (C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:3535:28)
    at listOnTimeout (node:internal/timers:569:17)
    at process.processTimers (node:internal/timers:512:7)
Memory: 33MB
Uptime: 8831s
---
[2025-06-27T00:55:25.523Z] UNHANDLEDREJECTION: The reply to this interaction has already been sent or deferred.
Stack: Error [InteractionAlreadyReplied]: The reply to this interaction has already been sent or deferred.
    at ModalSubmitInteraction.update (C:\Users\<USER>\Desktop\bot de vendas\node_modules\discord.js\src\structures\interfaces\InteractionResponses.js:342:46)
    at UserBot.handleConfigurarBoasVindas (C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:12485:27)
    at Timeout._onTimeout (C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:3598:28)
    at listOnTimeout (node:internal/timers:569:17)
    at process.processTimers (node:internal/timers:512:7)
Memory: 34MB
Uptime: 9012s
---
[2025-06-27T00:58:03.037Z] UNHANDLEDREJECTION: The reply to this interaction has already been sent or deferred.
Stack: Error [InteractionAlreadyReplied]: The reply to this interaction has already been sent or deferred.
    at ModalSubmitInteraction.update (C:\Users\<USER>\Desktop\bot de vendas\node_modules\discord.js\src\structures\interfaces\InteractionResponses.js:342:46)
    at UserBot.handleConfigurarMensagemBoasVindas (C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:12807:27)
    at Timeout._onTimeout (C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:3491:32)
    at listOnTimeout (node:internal/timers:569:17)
    at process.processTimers (node:internal/timers:512:7)
Memory: 35MB
Uptime: 9169s
---
[2025-06-27T00:58:38.024Z] UNHANDLEDREJECTION: The reply to this interaction has already been sent or deferred.
Stack: Error [InteractionAlreadyReplied]: The reply to this interaction has already been sent or deferred.
    at ModalSubmitInteraction.update (C:\Users\<USER>\Desktop\bot de vendas\node_modules\discord.js\src\structures\interfaces\InteractionResponses.js:342:46)
    at UserBot.handleConfigurarMensagemBoasVindas (C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:12807:27)
    at Timeout._onTimeout (C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:3535:28)
    at listOnTimeout (node:internal/timers:569:17)
    at process.processTimers (node:internal/timers:512:7)
Memory: 35MB
Uptime: 9204s
---
[2025-06-27T01:26:03.099Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-27T01:34:32.667Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-27T01:34:48.753Z] WRAPPEDASYNC: The reply to this interaction has already been sent or deferred.
Stack: Error [InteractionAlreadyReplied]: The reply to this interaction has already been sent or deferred.
    at ModalSubmitInteraction.update (C:\Users\<USER>\Desktop\bot de vendas\node_modules\discord.js\src\structures\interfaces\InteractionResponses.js:342:46)
    at UserBot.handleConfigurarBoasVindas (C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:12460:27)
    at UserBot.handleModalSubmit (C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:3568:24)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:127:21
    at async Client.<anonymous> (C:\Users\<USER>\Desktop\bot de vendas\anticrash-system.js:186:24)
Memory: 31MB
Uptime: 195s
---
[2025-06-27T01:34:48.756Z] UNCAUGHTEXCEPTION: The reply to this interaction has already been sent or deferred.
Stack: Error [InteractionAlreadyReplied]: The reply to this interaction has already been sent or deferred.
    at ModalSubmitInteraction.update (C:\Users\<USER>\Desktop\bot de vendas\node_modules\discord.js\src\structures\interfaces\InteractionResponses.js:342:46)
    at UserBot.handleConfigurarBoasVindas (C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:12460:27)
    at UserBot.handleModalSubmit (C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:3568:24)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:127:21
    at async Client.<anonymous> (C:\Users\<USER>\Desktop\bot de vendas\anticrash-system.js:186:24)
Memory: 31MB
Uptime: 195s
---
[2025-06-27T01:35:07.957Z] WRAPPEDASYNC: The reply to this interaction has already been sent or deferred.
Stack: Error [InteractionAlreadyReplied]: The reply to this interaction has already been sent or deferred.
    at ModalSubmitInteraction.update (C:\Users\<USER>\Desktop\bot de vendas\node_modules\discord.js\src\structures\interfaces\InteractionResponses.js:342:46)
    at UserBot.handleConfigurarBoasVindas (C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:12460:27)
    at UserBot.handleModalSubmit (C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:3517:24)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:127:21
    at async Client.<anonymous> (C:\Users\<USER>\Desktop\bot de vendas\anticrash-system.js:186:24)
Memory: 32MB
Uptime: 214s
---
[2025-06-27T01:35:07.960Z] UNCAUGHTEXCEPTION: The reply to this interaction has already been sent or deferred.
Stack: Error [InteractionAlreadyReplied]: The reply to this interaction has already been sent or deferred.
    at ModalSubmitInteraction.update (C:\Users\<USER>\Desktop\bot de vendas\node_modules\discord.js\src\structures\interfaces\InteractionResponses.js:342:46)
    at UserBot.handleConfigurarBoasVindas (C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:12460:27)
    at UserBot.handleModalSubmit (C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:3517:24)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:127:21
    at async Client.<anonymous> (C:\Users\<USER>\Desktop\bot de vendas\anticrash-system.js:186:24)
Memory: 32MB
Uptime: 214s
---
[2025-06-27T01:47:21.871Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-27T01:47:56.106Z] WRAPPEDASYNC: The reply to this interaction has already been sent or deferred.
Stack: Error [InteractionAlreadyReplied]: The reply to this interaction has already been sent or deferred.
    at ModalSubmitInteraction.update (C:\Users\<USER>\Desktop\bot de vendas\node_modules\discord.js\src\structures\interfaces\InteractionResponses.js:342:46)
    at UserBot.handleConfigurarBoasVindas (C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:12466:27)
    at UserBot.handleModalSubmit (C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:3574:24)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:133:21
    at async Client.<anonymous> (C:\Users\<USER>\Desktop\bot de vendas\anticrash-system.js:186:24)
Memory: 32MB
Uptime: 47s
---
[2025-06-27T01:47:56.108Z] UNCAUGHTEXCEPTION: The reply to this interaction has already been sent or deferred.
Stack: Error [InteractionAlreadyReplied]: The reply to this interaction has already been sent or deferred.
    at ModalSubmitInteraction.update (C:\Users\<USER>\Desktop\bot de vendas\node_modules\discord.js\src\structures\interfaces\InteractionResponses.js:342:46)
    at UserBot.handleConfigurarBoasVindas (C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:12466:27)
    at UserBot.handleModalSubmit (C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:3574:24)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:133:21
    at async Client.<anonymous> (C:\Users\<USER>\Desktop\bot de vendas\anticrash-system.js:186:24)
Memory: 32MB
Uptime: 47s
---
[2025-06-27T01:48:04.848Z] WRAPPEDASYNC: The reply to this interaction has already been sent or deferred.
Stack: Error [InteractionAlreadyReplied]: The reply to this interaction has already been sent or deferred.
    at ModalSubmitInteraction.update (C:\Users\<USER>\Desktop\bot de vendas\node_modules\discord.js\src\structures\interfaces\InteractionResponses.js:342:46)
    at UserBot.handleConfigurarBoasVindas (C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:12466:27)
    at UserBot.handleModalSubmit (C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:3523:24)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:133:21
    at async Client.<anonymous> (C:\Users\<USER>\Desktop\bot de vendas\anticrash-system.js:186:24)
Memory: 33MB
Uptime: 56s
---
[2025-06-27T01:48:04.850Z] UNCAUGHTEXCEPTION: The reply to this interaction has already been sent or deferred.
Stack: Error [InteractionAlreadyReplied]: The reply to this interaction has already been sent or deferred.
    at ModalSubmitInteraction.update (C:\Users\<USER>\Desktop\bot de vendas\node_modules\discord.js\src\structures\interfaces\InteractionResponses.js:342:46)
    at UserBot.handleConfigurarBoasVindas (C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:12466:27)
    at UserBot.handleModalSubmit (C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:3523:24)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:133:21
    at async Client.<anonymous> (C:\Users\<USER>\Desktop\bot de vendas\anticrash-system.js:186:24)
Memory: 33MB
Uptime: 56s
---
[2025-06-27T01:52:03.686Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-27T02:15:40.066Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-27T02:17:37.787Z] UNHANDLEDREJECTION: The reply to this interaction has already been sent or deferred.
Stack: Error [InteractionAlreadyReplied]: The reply to this interaction has already been sent or deferred.
    at ModalSubmitInteraction.update (C:\Users\<USER>\Desktop\bot de vendas\node_modules\discord.js\src\structures\interfaces\InteractionResponses.js:342:46)
    at UserBot.handleGerenciarProdutos (C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:9427:27)
    at Timeout._onTimeout (C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:3178:32)
    at listOnTimeout (node:internal/timers:569:17)
    at process.processTimers (node:internal/timers:512:7)
Memory: 33MB
Uptime: 175s
---
[2025-06-27T03:19:18.439Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-27T13:15:45.313Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-27T13:32:39.919Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-27T13:34:10.433Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-27T13:40:50.704Z] GENERAL: Erro no servidor: listen EADDRINUSE: address already in use :::3003
Memory: N/A
Uptime: N/A
---
[2025-06-27T14:23:54.797Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-27T14:26:49.384Z] GENERAL: Erro no servidor: listen EADDRINUSE: address already in use :::3003
Memory: N/A
Uptime: N/A
---
[2025-06-27T15:24:50.160Z] GENERAL: Erro no servidor: listen EADDRINUSE: address already in use :::3003
Memory: N/A
Uptime: N/A
---
[2025-06-27T15:34:22.510Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-27T15:38:31.410Z] GENERAL: Erro no servidor: listen EADDRINUSE: address already in use :::3003
Memory: N/A
Uptime: N/A
---
[2025-06-27T15:44:00.395Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-27T15:52:53.219Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-27T16:04:10.569Z] GENERAL: Erro no servidor: listen EADDRINUSE: address already in use :::3003
Memory: N/A
Uptime: N/A
---
[2025-06-27T16:11:55.496Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
