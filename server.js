require('dotenv').config();

const express = require('express');
const axios = require('axios');
const path = require('path');
const MainBot = require('./main-bot');
const SupabaseDatabase = require('./supabase-database');
const database = new SupabaseDatabase();
const BotManager = require('./bot-manager');
const botManager = new BotManager(database);
const { generateJWT, verifyJWT, generateUrlToken, generateSessionToken } = require('./jwt-utils');
const { authenticateJWT, authenticateSession, authenticateHybrid } = require('./auth-middleware');
const {
    basicSecurity,
    corsMiddleware,
    validateInput,
    detectInjection,
    securityLogger,
    sanitizeForLog,
    authRateLimit,
    generalRateLimit,
    csrfProtection,
    pathTraversalProtection,
    prototypePollutionProtection
} = require('./security-middleware');
const { raceConditionProtection } = require('./race-condition-protection');
const { secureErrorHandler, createSecureErrors, asyncErrorHandler } = require('./secure-error-handler');
const antiCrashSystem = require('./anticrash-system');
const BasicBot = require('./basic-bot');
const { SAAS_BOTS_CONFIG, BOT_INVITE_URLS } = require('./saas-bots-config');

const app = express();
const mainBot = new MainBot();

// Instâncias dos bots SaaS
let basicBot = null;
let proBot = null;

// Função para inicializar bots SaaS
async function initializeSaaSBots() {
    try {
        console.log('🤖 Inicializando bots SaaS centralizados...');

        // Inicializar bot básico se o token estiver configurado
        if (process.env.BASIC_BOT_TOKEN) {
            console.log('🔵 Iniciando Nodex Bot Basic...');
            basicBot = new BasicBot();
            await basicBot.start();
        } else {
            console.log('⚠️ Token do bot básico não configurado');
        }

        // Inicializar bot pro se o token estiver configurado
        if (process.env.PRO_BOT_TOKEN) {
            console.log('🟣 Iniciando Nodex Bot Pro...');
            // proBot = new ProBot(); // Implementar depois
            // await proBot.start();
            console.log('🚧 Bot Pro será implementado em breve');
        } else {
            console.log('⚠️ Token do bot pro não configurado');
        }

        console.log('✅ Bots SaaS inicializados com sucesso!');
    } catch (error) {
        console.error('❌ Erro ao inicializar bots SaaS:', error);
    }
}

// Configuração do Stripe (só inicializar se a chave estiver disponível)
let stripe = null;
if (process.env.STRIPE_SECRET_KEY) {
    stripe = require('stripe')(process.env.STRIPE_SECRET_KEY);
    // console.log('✅ Stripe configurado');
} else {
    console.log('⚠️ STRIPE_SECRET_KEY não encontrada - funcionalidades de pagamento desabilitadas');
}

// Configuração do Supabase (só inicializar se as chaves estiverem disponíveis)
let supabase = null;
if (process.env.SUPABASE_URL && process.env.SUPABASE_SERVICE_ROLE_KEY) {
    const { createClient } = require('@supabase/supabase-js');
    supabase = createClient(
        process.env.SUPABASE_URL,
        process.env.SUPABASE_SERVICE_ROLE_KEY
    );
    // console.log('✅ Supabase configurado');
} else {
    console.log('⚠️ Configurações do Supabase não encontradas - funcionalidades de licença desabilitadas');
}

// Configuração do Discord OAuth2
const DISCORD_CLIENT_ID = process.env.DISCORD_CLIENT_ID;
const DISCORD_CLIENT_SECRET = process.env.DISCORD_CLIENT_SECRET;
const DISCORD_REDIRECT_URI = process.env.DISCORD_REDIRECT_URI || 'http://localhost:3003/callback';

// Verificar se as variáveis de ambiente estão configuradas corretamente
if (!DISCORD_CLIENT_ID || !DISCORD_CLIENT_SECRET) {
    console.error('Erro: Variáveis de ambiente não configuradas corretamente');
    console.error('DISCORD_CLIENT_ID:', DISCORD_CLIENT_ID);
    console.error('DISCORD_CLIENT_SECRET:', DISCORD_CLIENT_SECRET);
    process.exit(1);
}

// Verificar se o client_id é um número válido
const isValidSnowflake = (id) => {
    return typeof id === 'string' && id.length >= 17 && id.length <= 19 && !isNaN(id);
};

if (!isValidSnowflake(DISCORD_CLIENT_ID)) {
    console.error('Erro: Client ID inválido. Deve ser um número de 17 a 19 dígitos.');
    process.exit(1);
}

console.log('Configuração do Discord:');
console.log('Client ID:', DISCORD_CLIENT_ID);
console.log('Redirect URI:', DISCORD_REDIRECT_URI);

// Middlewares de segurança
app.use(basicSecurity);
app.use(corsMiddleware);
app.use(pathTraversalProtection);
app.use(prototypePollutionProtection);
app.use(generalRateLimit);
app.use(validateInput);
app.use(detectInjection);
app.use(csrfProtection);
app.use(securityLogger);

// Servir arquivos estáticos do React build
app.use(express.static(path.join(__dirname, 'discord-sales-oasis-main/dist')));

// Middleware para JSON
app.use(express.json());

// Rotas da API
app.get('/api/stats', (req, res) => {
    const stats = {
        ...database.getStats(),
        ...botManager.getStats()
    };
    res.json(stats);
});

// Rota para configurar bot
app.post('/api/configure-bot', antiCrashSystem.wrapAsync(async (req, res) => {
    try {
        const { clientId, token, username, userId } = req.body;

        console.log(`🔧 Configurando bot para ${username} (ID: ${userId})...`);

        // Validar dados
        if (!botManager.validateClientId(clientId)) {
            return res.json({ success: false, message: 'Client ID inválido' });
        }

        // Validar token
        const isValidToken = await botManager.validateDiscordToken(token);
        if (!isValidToken) {
            return res.json({ success: false, message: 'Token inválido' });
        }

        // Usar userId se disponível, senão usar username
        const userKey = userId || username;

        // Salvar dados do bot no database
        const userData = database.getUser(userKey) || {};

        // Configurar dados do novo bot (substituir completamente os dados antigos)
        userData.clientId = clientId;
        userData.token = token;
        userData.botToken = token; // Garantir que ambos os campos sejam atualizados
        userData.username = username;
        userData.id = userId || username;
        userData.botActive = false; // Será marcado como true quando o bot for iniciado
        userData.botStartedAt = null;
        userData.botStoppedAt = null;

        // Limpar flags de token inválido se existirem
        userData.tokenInvalid = false;
        userData.tokenInvalidAt = null;

        console.log(`🔄 Configurando novo bot - limpando dados antigos e salvando novos dados`);
        database.saveUser(userKey, userData);

        // Forçar recarga dos dados do Supabase para garantir sincronização
        await database.loadFromSupabase();

        console.log(`💾 Dados salvos para usuário ${userKey}:`, userData);

        // Criar bot do usuário
        const result = await botManager.createUserBot(userKey, clientId, token);

        if (result.success) {
            console.log(`✅ Bot criado para ${username}`);
            res.json({ success: true, message: 'Aplicação configurada com sucesso!' });
        } else {
            console.log(`❌ Falha ao criar bot para ${username}: ${result.message}`);
            res.json({ success: false, message: result.message });
        }
    } catch (error) {
        console.error('Erro ao configurar bot:', error);
        antiCrashSystem.logError(`Erro ao configurar bot: ${error.message}`);
        res.json({ success: false, message: 'Erro interno do servidor' });
    }
}));

// Rota para verificar status do bot
app.get('/api/bot-status', (req, res) => {
    const stats = botManager.getStats();
    res.json({ success: true, bots: stats.botsList });
});

// Rota para parar bot
app.post('/api/stop-bot', antiCrashSystem.wrapAsync(async (req, res) => {
    try {
        // Por simplicidade, vamos parar todos os bots
        // Em produção, você identificaria o usuário específico
        const activeBots = botManager.getAllActiveBots();
        for (const bot of activeBots) {
            await botManager.stopUserBot(bot.userId);
        }
        res.json({ success: true });
    } catch (error) {
        antiCrashSystem.logError(`Erro ao parar bot: ${error.message}`);
        res.json({ success: false, message: 'Erro ao parar bot' });
    }
}));

// Rota para obter informações do usuário logado (com autenticação híbrida)
app.get('/api/user-info', authenticateHybrid, (req, res) => {
    res.json({
        id: req.user.id,
        username: req.user.username,
        discriminator: req.user.discriminator,
        avatar: req.user.avatar,
        authType: req.user.authType || 'session'
    });
});

// Rota para obter dados completos do usuário (incluindo urlToken)
app.get('/api/user-full', authenticateHybrid, (req, res) => {
    res.json({
        id: req.user.id,
        username: req.user.username,
        discriminator: req.user.discriminator,
        avatar: req.user.avatar,
        urlToken: req.user.fullData.urlToken,
        authType: req.user.authType || 'session'
    });
});

// APIs para o sistema SaaS

// Buscar configuração do usuário
app.get('/api/user-config/:discordId', authenticateHybrid, antiCrashSystem.wrapAsync(async (req, res) => {
    try {
        const { discordId } = req.params;

        // Verificar se o usuário pode acessar esta configuração
        if (req.user.id !== discordId) {
            return res.status(403).json({ error: 'Acesso negado' });
        }

        const { data, error } = await database.supabase
            .from('user_configurations')
            .select('*')
            .eq('discord_id', discordId)
            .single();

        if (error) {
            if (error.code === 'PGRST116') {
                return res.status(404).json({ error: 'Configuração não encontrada' });
            }
            throw error;
        }

        res.json(data);
    } catch (error) {
        console.error('Erro ao buscar configuração do usuário:', error);
        res.status(500).json({ error: 'Erro interno do servidor' });
    }
}));

// Criar configuração do usuário
app.post('/api/user-config', authenticateHybrid, antiCrashSystem.wrapAsync(async (req, res) => {
    try {
        const {
            discord_id,
            discord_username,
            license_plan,
            license_validated
        } = req.body;

        // Verificar se o usuário pode criar esta configuração
        if (req.user.id !== discord_id) {
            return res.status(403).json({ error: 'Acesso negado' });
        }

        // Verificar se já existe configuração
        const { data: existing } = await database.supabase
            .from('user_configurations')
            .select('id')
            .eq('discord_id', discord_id)
            .single();

        if (existing) {
            return res.status(409).json({ error: 'Configuração já existe' });
        }

        // Criar nova configuração
        const { data, error } = await database.supabase
            .from('user_configurations')
            .insert([{
                discord_id,
                discord_username,
                discord_discriminator: req.user.discriminator,
                discord_avatar: req.user.avatar,
                license_plan: license_plan || 'basic',
                license_validated: license_validated || false,
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString(),
                last_activity: new Date().toISOString()
            }])
            .select()
            .single();

        if (error) {
            throw error;
        }

        res.status(201).json(data);
    } catch (error) {
        console.error('Erro ao criar configuração do usuário:', error);
        res.status(500).json({ error: 'Erro interno do servidor' });
    }
}));

// Atualizar configuração do usuário
app.put('/api/user-config/:discordId', authenticateHybrid, antiCrashSystem.wrapAsync(async (req, res) => {
    try {
        const { discordId } = req.params;
        const updateData = req.body;

        // Verificar se o usuário pode atualizar esta configuração
        if (req.user.id !== discordId) {
            return res.status(403).json({ error: 'Acesso negado' });
        }

        // Adicionar timestamps
        updateData.updated_at = new Date().toISOString();
        updateData.last_activity = new Date().toISOString();

        const { data, error } = await database.supabase
            .from('user_configurations')
            .update(updateData)
            .eq('discord_id', discordId)
            .select()
            .single();

        if (error) {
            if (error.code === 'PGRST116') {
                return res.status(404).json({ error: 'Configuração não encontrada' });
            }
            throw error;
        }

        res.json(data);
    } catch (error) {
        console.error('Erro ao atualizar configuração do usuário:', error);
        res.status(500).json({ error: 'Erro interno do servidor' });
    }
}));

// Buscar bots disponíveis
app.get('/api/saas-bots', authenticateHybrid, (req, res) => {
    const bots = Object.values(SAAS_BOTS_CONFIG).map(bot => ({
        id: bot.id,
        name: bot.name,
        plan: bot.plan,
        avatar: '/placeholder.svg', // Você pode adicionar avatars específicos
        invite_url: BOT_INVITE_URLS[bot.plan],
        description: bot.plan === 'basic' ? 'Bot básico com funcionalidades essenciais para vendas' : 'Bot profissional com recursos avançados',
        features: bot.features,
        status: 'online' // Você pode implementar verificação real de status
    }));

    res.json(bots);
});

// Rota para renovar token JWT (com proteção contra race condition)
app.post('/api/refresh-token',
    authenticateHybrid,
    raceConditionProtection(req => `refresh-token-${req.user?.id || req.ip}`),
    asyncErrorHandler(async (req, res) => {
        // Gerar novo JWT token
        const newJwtToken = generateJWT({
            id: req.user.id,
            username: req.user.username,
            discriminator: req.user.discriminator,
            avatar: req.user.avatar
        });

        // Atualizar token no banco de dados e invalidar token de sessão antigo
        const userData = req.user.fullData;
        userData.jwtToken = newJwtToken;
        userData.sessionToken = generateSessionToken(); // Gerar novo token de sessão
        userData.updatedAt = new Date();
        database.saveUser(req.user.id, userData);

        res.json({
            success: true,
            token: newJwtToken,
            sessionToken: userData.sessionToken,
            expiresIn: '24h'
        });
    }));

// Rota para logout (invalidar tokens)
app.post('/api/logout', authenticateHybrid, (req, res) => {
    try {
        // Invalidar tokens do usuário
        const userData = req.user.fullData;
        userData.sessionToken = null;
        userData.jwtToken = null;
        userData.updatedAt = new Date();
        database.saveUser(req.user.id, userData);

        res.json({
            success: true,
            message: 'Logout realizado com sucesso'
        });
    } catch (error) {
        console.error('Erro ao fazer logout:', error);
        res.status(500).json({
            success: false,
            message: 'Erro interno do servidor'
        });
    }
});

// Rota para obter dados do usuário
app.get('/api/user-data', authenticateHybrid, (req, res) => {
    try {
        const userData = req.user.fullData;

        res.json({
            id: userData.id,
            username: userData.username,
            discriminator: userData.discriminator,
            avatar: userData.avatar,
            urlToken: userData.urlToken,
            loginCount: userData.loginCount,
            lastLogin: userData.lastLogin
        });
    } catch (error) {
        console.error('Erro ao obter dados do usuário:', error);
        res.status(500).json({ error: 'Erro interno do servidor' });
    }
});

// Rota para obter bots do usuário
app.get('/api/user-bots/:userId', async (req, res) => {
    const { userId } = req.params;

    console.log(`🔍 Buscando bots para usuário: ${userId}`);

    // Obter dados do usuário do database
    const userData = database.getUser(userId);
    const stats = botManager.getStats();

    console.log(`📊 Dados do usuário:`, userData);
    console.log(`📊 Stats do bot manager:`, stats);

    // Criar lista de bots do usuário
    const userBots = [];

    if (userData && userData.clientId) {
        console.log(`✅ Usuário tem bot configurado: ${userData.clientId}`);

        // Buscar informações do bot no Discord
        let botName = `Bot de ${userData.username || 'Usuário'}`;
        let botStatus = 'offline';
        let serverCount = 0;
        let botAvatar = null;

        try {
            // Verificar se o bot está ativo no botManager primeiro
            const activeBots = botManager.getAllActiveBots ? botManager.getAllActiveBots() : [];
            const activeBot = activeBots.find(bot => bot.userId === userId);

            console.log(`🔍 Bot ativo encontrado:`, activeBot);

            if (activeBot && activeBot.isActive) {
                botStatus = 'online';
                console.log(`✅ Bot está ativo no botManager`);
            } else {
                console.log(`❌ Bot não está ativo no botManager`);
            }

            // Tentar buscar nome do bot e contagem de servidores via API do Discord (se tiver token)
            const botToken = userData.token || userData.botToken;
            if (botToken) {
                try {
                    const axios = require('axios');

                    // Buscar informações do bot
                    const botResponse = await axios.get('https://discord.com/api/v10/users/@me', {
                        headers: {
                            'Authorization': `Bot ${botToken}`
                        }
                    });
                    botName = botResponse.data.username;

                    // Construir URL do avatar se existir
                    if (botResponse.data.avatar) {
                        botAvatar = `https://cdn.discordapp.com/avatars/${botResponse.data.id}/${botResponse.data.avatar}.png?size=128`;
                    } else {
                        // Avatar padrão do Discord se não tiver avatar customizado
                        const defaultAvatarIndex = parseInt(botResponse.data.discriminator) % 5;
                        botAvatar = `https://cdn.discordapp.com/embed/avatars/${defaultAvatarIndex}.png`;
                    }

                    // Buscar contagem de servidores
                    try {
                        const guildsResponse = await axios.get('https://discord.com/api/v10/users/@me/guilds', {
                            headers: {
                                'Authorization': `Bot ${botToken}`
                            }
                        });
                        serverCount = guildsResponse.data.length;

                        // Se conseguiu fazer a requisição e o bot está ativo no manager, está online
                        if (activeBot && activeBot.isActive) {
                            botStatus = 'online';
                        }
                    } catch (guildsError) {
                        console.log(`⚠️ Não foi possível buscar servidores: ${guildsError.message}`);
                        // Se não conseguiu buscar servidores mas o bot está ativo, manter online
                        if (activeBot && activeBot.isActive) {
                            botStatus = 'online';
                        } else {
                            botStatus = 'offline';
                        }
                    }
                } catch (error) {
                    console.log(`⚠️ Não foi possível buscar nome do bot: ${error.message}`);
                    // Se não conseguiu buscar info do bot, verificar se está ativo no manager
                    if (activeBot && activeBot.isActive) {
                        botStatus = 'online';
                    } else {
                        botStatus = 'offline';
                    }
                }
            }
        } catch (error) {
            console.log(`⚠️ Erro ao verificar status do bot: ${error.message}`);
            botStatus = 'offline';
        }

        // Verificar se o token está marcado como inválido
        const tokenInvalid = userData.tokenInvalid || false;
        const tokenInvalidAt = userData.tokenInvalidAt || null;

        userBots.push({
            id: userData.clientId,
            name: botName,
            clientId: userData.clientId,
            status: botStatus,
            servers: serverCount,
            sales: 0,
            lastSale: new Date().toLocaleDateString('pt-BR'),
            cluster: 'CLF004',
            tariff: 3,
            avatar: botAvatar,
            tokenInvalid: tokenInvalid,
            tokenInvalidAt: tokenInvalidAt,
            needsReconfiguration: tokenInvalid || (!userData.token && !userData.botToken)
        });
    } else {
        console.log(`❌ Usuário não tem bot configurado ou não encontrado`);
    }

    console.log(`📋 Retornando ${userBots.length} bots para o usuário`);
    res.json({ bots: userBots });
});

// Rota para ações do bot (start, stop, pause, delete)
app.post('/api/bot-action', antiCrashSystem.wrapAsync(async (req, res) => {
    try {
        const { botId, action } = req.body;

        console.log(`🔧 Executando ação ${action} no bot ${botId}`);

        // Encontrar dados do usuário pelo clientId
        let userData = null;
        let userKey = null;

        database.users.forEach((data, key) => {
            if (data.clientId === botId) {
                userData = data;
                userKey = key;
            }
        });

        if (!userData) {
            return res.json({ success: false, message: 'Bot não encontrado' });
        }

        switch (action) {
            case 'start':
                console.log(`🚀 Iniciando bot ${botId}...`);
                // Criar/iniciar bot do usuário
                const startResult = await botManager.createUserBot(userKey, userData.clientId, userData.token);
                if (startResult.success) {
                    console.log(`✅ Bot ${botId} iniciado com sucesso`);
                    res.json({ success: true, message: 'Bot iniciado com sucesso!' });
                } else {
                    console.log(`❌ Falha ao iniciar bot ${botId}: ${startResult.message}`);
                    res.json({ success: false, message: startResult.message });
                }
                break;

            case 'stop':
                console.log(`🛑 Parando bot ${botId}...`);
                // Parar bot do usuário
                const stopResult = await botManager.stopUserBot(userKey);
                if (stopResult.success) {
                    console.log(`✅ Bot ${botId} parado com sucesso`);
                    res.json({ success: true, message: 'Bot parado com sucesso!' });
                } else {
                    console.log(`❌ Falha ao parar bot ${botId}: ${stopResult.message}`);
                    res.json({ success: false, message: stopResult.message });
                }
                break;

            case 'pause':
                console.log(`⏸️ Pausando bot ${botId}...`);
                // Por enquanto, pausar = parar (pode implementar lógica específica depois)
                const pauseResult = await botManager.stopUserBot(userKey);
                if (pauseResult.success) {
                    console.log(`✅ Bot ${botId} pausado com sucesso`);
                    res.json({ success: true, message: 'Bot pausado com sucesso!' });
                } else {
                    console.log(`❌ Falha ao pausar bot ${botId}: ${pauseResult.message}`);
                    res.json({ success: false, message: pauseResult.message });
                }
                break;

            case 'restart':
                console.log(`🔄 Reiniciando bot ${botId}...`);
                // Parar primeiro
                const stopForRestart = await botManager.stopUserBot(userKey);
                console.log(`🛑 Resultado do stop para restart:`, stopForRestart);

                // Aguardar um pouco e iniciar novamente
                setTimeout(async () => {
                    const restartResult = await botManager.createUserBot(userKey, userData.clientId, userData.token);
                    console.log(`🔄 Resultado do restart:`, restartResult);
                }, 2000);

                res.json({ success: true, message: 'Bot reiniciado com sucesso!' });
                break;

            case 'delete':
                console.log(`🗑️ Deletando bot ${botId}...`);
                // Parar bot e remover dados
                const deleteStopResult = await botManager.stopUserBot(userKey);
                console.log(`🛑 Resultado do stop para delete:`, deleteStopResult);

                // Limpar dados do bot no Supabase (manter usuário, mas remover dados do bot)
                // userData já está disponível do escopo anterior
                if (userData) {
                    // Limpar apenas dados relacionados ao bot
                    userData.clientId = null;
                    userData.token = null;
                    userData.botToken = null;
                    userData.botActive = false;
                    userData.botStartedAt = null;
                    userData.botStoppedAt = new Date().toISOString();
                    userData.tokenInvalid = false;
                    userData.tokenInvalidAt = null;

                    // Salvar no Supabase com dados limpos
                    await database.saveUser(userKey, userData);
                    console.log(`🧹 Dados do bot limpos no Supabase para usuário ${userKey}`);
                } else {
                    // Se não há dados do usuário, remover completamente do cache
                    database.users.delete(userKey);
                }

                console.log(`✅ Bot ${botId} deletado com sucesso`);

                res.json({ success: true, message: 'Bot deletado com sucesso!' });
                break;

            default:
                res.json({ success: false, message: 'Ação inválida' });
        }
    } catch (error) {
        console.error('Erro ao executar ação do bot:', error);
        antiCrashSystem.logError(`Erro ao executar ação do bot: ${error.message}`);
        res.json({ success: false, message: 'Erro interno do servidor' });
    }
}));

// Rota de callback do Discord (sem rate limiting muito restritivo)
app.get('/callback', antiCrashSystem.wrapAsync(async (req, res) => {
    const code = req.query.code;
    const token = req.query.token;

    // Se não há code mas há token, é uma requisição do React - servir o app
    if (!code && token) {
        return res.sendFile(path.join(__dirname, 'discord-sales-oasis-main/dist/index.html'));
    }

    // Se não há code nem token, é uma requisição inválida
    if (!code) {
        return res.status(400).send('Erro: Código não fornecido');
    }

    try {
        // Trocar código por token de acesso
        const tokenData = new URLSearchParams({
            client_id: DISCORD_CLIENT_ID,
            client_secret: DISCORD_CLIENT_SECRET,
            grant_type: 'authorization_code',
            code,
            redirect_uri: DISCORD_REDIRECT_URI,
            scope: 'identify guilds.join guilds'
        });

        const tokenResponse = await axios.post('https://discord.com/api/v10/oauth2/token', tokenData, {
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded'
            }
        });

        // Obter informações do usuário
        const userResponse = await axios.get('https://discord.com/api/v10/users/@me', {
            headers: {
                authorization: `Bearer ${tokenResponse.data.access_token}`
            }
        });

        const userData = {
            id: userResponse.data.id,
            username: userResponse.data.username,
            discriminator: userResponse.data.discriminator,
            avatar: userResponse.data.avatar,
            accessToken: tokenResponse.data.access_token
        };

        console.log(`✅ Usuário autenticado: ${userData.username}#${userData.discriminator}`);

        // Gerar JWT token
        const jwtToken = generateJWT(userData);

        // Gerar token de sessão seguro (compatibilidade)
        const sessionToken = generateSessionToken();

        // Gerar token aleatório para URL (segurança adicional)
        const urlToken = generateUrlToken();

        // Manter dados existentes do usuário se já existir
        const existingUserData = database.getUser(userData.id) || {};

        // Invalidar tokens antigos para evitar session fixation
        if (existingUserData.sessionToken) {
            console.log('🔄 Invalidando token de sessão anterior para segurança');
        }
        if (existingUserData.jwtToken) {
            console.log('🔄 Invalidando JWT anterior para segurança');
        }

        // Mesclar dados mantendo clientId e token se existirem
        const mergedUserData = {
            ...existingUserData,
            id: userData.id,
            username: userData.username,
            discriminator: userData.discriminator,
            avatar: userData.avatar,
            accessToken: userData.accessToken,
            jwtToken: jwtToken,
            sessionToken: sessionToken,
            urlToken: urlToken,
            createdAt: existingUserData.createdAt || new Date(),
            updatedAt: new Date(),
            lastLogin: new Date(),
            loginCount: (existingUserData.loginCount || 0) + 1
        };

        // Salvar dados mesclados
        database.saveUser(userData.id, mergedUserData);

        console.log(`💾 Dados salvos para usuário ${userData.id}:`, sanitizeForLog(mergedUserData));

        // Sempre redirecionar para a página de callback do React
        // O frontend React irá decidir se vai para dashboard ou checkout baseado no localStorage
        // Não passar o código novamente pois já foi usado
        res.redirect(`/callback?token=${sessionToken}&urlToken=${urlToken}`);
    } catch (error) {
        console.error('Erro na autenticação:', error);
        antiCrashSystem.logError(`Erro na autenticação: ${error.message}`);
        res.redirect('/?error=auth_failed');
    }
}));

// Rota de callback específica para planos (vai direto para checkout)
app.get('/plan-callback', antiCrashSystem.wrapAsync(async (req, res) => {
    const code = req.query.code;
    const token = req.query.token;
    const state = req.query.state; // Plano vem do state

    // Se não há code mas há token, é uma requisição do React - servir o app
    if (!code && token) {
        return res.sendFile(path.join(__dirname, 'discord-sales-oasis-main/dist/index.html'));
    }

    // Se não há code nem token, é uma requisição inválida
    if (!code) {
        return res.status(400).send('Erro: Código não fornecido');
    }

    try {
        console.log('🛒 Processando callback de plano...');

        // Trocar código por token de acesso
        const tokenResponse = await axios.post('https://discord.com/api/v10/oauth2/token',
            `client_id=${process.env.DISCORD_CLIENT_ID}&client_secret=${process.env.DISCORD_CLIENT_SECRET}&grant_type=authorization_code&code=${code}&redirect_uri=${encodeURIComponent(process.env.DISCORD_REDIRECT_URI_PLAN || 'http://localhost:3003/plan-callback')}&scope=identify+guilds.join+guilds`,
            {
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded'
                }
            }
        );

        const { access_token } = tokenResponse.data;

        // Obter dados do usuário
        const userResponse = await axios.get('https://discord.com/api/v10/users/@me', {
            headers: {
                'Authorization': `Bearer ${access_token}`
            }
        });

        const user = userResponse.data;
        console.log(`✅ Usuário autenticado para plano: ${user.username}#${user.discriminator}`);

        // Criar dados do usuário
        const userData = {
            id: user.id,
            username: user.username,
            discriminator: user.discriminator,
            avatar: user.avatar,
            accessToken: access_token
        };

        // Gerar JWT token
        const jwtToken = generateJWT(userData);

        // Gerar token de sessão seguro (compatibilidade)
        const sessionToken = generateSessionToken();

        // Gerar token aleatório para URL (segurança adicional)
        const urlToken = generateUrlToken();

        // Manter dados existentes do usuário se já existir
        const existingUserData = database.getUser(userData.id) || {};

        // Invalidar tokens antigos para evitar session fixation
        if (existingUserData.sessionToken) {
            console.log('🔄 Invalidando token de sessão anterior para segurança');
        }
        if (existingUserData.jwtToken) {
            console.log('🔄 Invalidando JWT anterior para segurança');
        }

        // Mesclar dados mantendo clientId e token se existirem
        const mergedUserData = {
            ...existingUserData,
            id: userData.id,
            username: userData.username,
            discriminator: userData.discriminator,
            avatar: userData.avatar,
            accessToken: userData.accessToken,
            jwtToken: jwtToken,
            sessionToken: sessionToken,
            urlToken: urlToken,
            createdAt: existingUserData.createdAt || new Date(),
            updatedAt: new Date(),
            lastLogin: new Date(),
            loginCount: (existingUserData.loginCount || 0) + 1
        };

        // Salvar no banco de dados
        database.saveUser(mergedUserData.id, mergedUserData);
        console.log('💾 Dados salvos com sucesso');

        // Redirecionar para a página de callback do React para planos
        res.redirect(`/plan-callback?token=${sessionToken}&urlToken=${urlToken}`);
    } catch (error) {
        console.error('Erro na autenticação do plano:', error);
        antiCrashSystem.logError(`Erro na autenticação do plano: ${error.message}`);
        res.redirect('/?error=auth_failed');
    }
}));

// Rota para integração com o sistema de autenticação Discord (para APIs do frontend)
app.post('/api/auth/discord', antiCrashSystem.wrapAsync(async (req, res) => {
    try {
        const { user, guilds, access_token } = req.body;

        if (!user || !user.id) {
            return res.status(400).json({ error: 'Dados do usuário inválidos' });
        }

        // Gerar tokens para o usuário
        const urlToken = generateUrlToken();
        const sessionToken = generateSessionToken();

        // Preparar dados do usuário para salvar
        const userData = {
            id: user.id,
            username: user.username,
            discriminator: user.discriminator,
            avatar: user.avatar,
            email: user.email,
            token: access_token,
            urlToken: urlToken,
            sessionToken: sessionToken,
            guilds: guilds || [],
            lastLogin: new Date().toISOString()
        };

        // Verificar se já existe dados para este usuário
        const existingData = database.getUser(user.id);

        // Mesclar dados existentes com novos dados
        const mergedUserData = existingData ? { ...existingData, ...userData } : userData;

        // Salvar dados mesclados
        database.saveUser(user.id, mergedUserData);

        console.log(`💾 Dados salvos via API para usuário ${user.id}:`, sanitizeForLog(mergedUserData));

        // Retornar tokens para o frontend
        res.json({
            sessionToken: sessionToken,
            urlToken: urlToken,
            user: {
                id: user.id,
                username: user.username,
                discriminator: user.discriminator,
                avatar: user.avatar,
                email: user.email
            }
        });

    } catch (error) {
        console.error('Erro na API de autenticação Discord:', error);
        antiCrashSystem.logError(`Erro na API de autenticação Discord: ${error.message}`);
        res.status(500).json({ error: 'Erro interno do servidor' });
    }
}));

// Rota para dashboard sem token - redirecionar para login
app.get('/dashboard', (req, res) => {
    // Se acessar /dashboard diretamente, redirecionar para login
    // O frontend vai lidar com a lógica de redirecionamento baseado no localStorage
    res.sendFile(path.join(__dirname, 'discord-sales-oasis-main/dist/index.html'));
});

// Rota específica para dashboard com token na URL
app.get('/dashboard/:urlToken', (req, res) => {
    const { urlToken } = req.params;

    // Verificar se o urlToken é válido
    let validToken = false;
    database.users.forEach((userData) => {
        if (userData.urlToken === urlToken) {
            validToken = true;
        }
    });

    if (validToken) {
        res.sendFile(path.join(__dirname, 'discord-sales-oasis-main/dist/index.html'));
    } else {
        res.redirect('/login');
    }
});

// Rota para extrato sem token - deixar o frontend lidar
app.get('/extrato', (req, res) => {
    res.sendFile(path.join(__dirname, 'discord-sales-oasis-main/dist/index.html'));
});

// Rota para página de extrato Nodex Pay com token na URL
app.get('/extrato/:urlToken', (req, res) => {
    const { urlToken } = req.params;

    // Verificar se o urlToken é válido
    let validToken = false;
    database.users.forEach((userData) => {
        if (userData.urlToken === urlToken) {
            validToken = true;
        }
    });

    if (validToken) {
        res.sendFile(path.join(__dirname, 'discord-sales-oasis-main/dist/index.html'));
    } else {
        res.redirect('/login');
    }
});

// API para obter dados de extrato do usuário (com autenticação)
app.get('/api/extrato/:userId', authenticateHybrid, (req, res) => {
    const { userId } = req.params;

    // Verificar se o usuário autenticado pode acessar estes dados
    if (req.user.id !== userId) {
        return res.status(403).json({
            error: 'Acesso negado',
            message: 'Você não tem permissão para acessar estes dados'
        });
    }

    // Obter dados do usuário
    const userData = database.getUser(userId);

    if (!userData) {
        return res.status(404).json({ error: 'Usuário não encontrado' });
    }

    // Simular dados de extrato (em produção, buscar de um banco de dados real)
    const extratoData = {
        userId: userId,
        username: userData.username,
        servers: [
            {
                id: userData.clientId || '123456789',
                name: `Servidor de ${userData.username}`,
                balance: Math.floor(Math.random() * 1000) / 100, // Valor aleatório para demonstração
                pendingWithdrawals: Math.floor(Math.random() * 500) / 100,
                lastUpdate: new Date().toISOString(),
                transactions: [
                    {
                        id: '1',
                        type: 'sale',
                        amount: 25.50,
                        description: 'Venda de produto premium',
                        date: new Date(Date.now() - 86400000).toISOString(),
                        status: 'completed'
                    },
                    {
                        id: '2',
                        type: 'withdrawal',
                        amount: -15.00,
                        description: 'Saque via Pix',
                        date: new Date(Date.now() - 172800000).toISOString(),
                        status: 'pending'
                    }
                ]
            }
        ]
    };

    res.json(extratoData);
});

// ==================== ROTAS DE PAGAMENTO E LICENÇAS ====================

// Função para gerar chave de licença
function generateLicenseKey() {
    const crypto = require('crypto');
    const segments = [];
    for (let i = 0; i < 4; i++) {
        const segment = crypto.randomBytes(2).toString('hex').toUpperCase();
        segments.push(segment);
    }
    return `NODEX-${segments.join('-')}`;
}

// Rota para criar licença gratuita
app.post('/api/licenses/create-free', authenticateHybrid, antiCrashSystem.wrapAsync(async (req, res) => {
    try {
        if (!supabase) {
            return res.status(503).json({ error: 'Serviço de licenças não disponível' });
        }
        const userId = req.user.id;
        const userEmail = req.user.email || `${req.user.username}@discord.user`;
        const userName = `${req.user.username}#${req.user.discriminator}`;

        // Verificar se o usuário já tem uma licença básica
        const { data: existingLicense } = await supabase
            .from('licenses')
            .select('license_key')
            .eq('discord_id', userId)
            .eq('plan', 'basic')
            .single();

        if (existingLicense) {
            return res.json({ licenseKey: existingLicense.license_key });
        }

        // Criar nova licença gratuita
        const licenseKey = generateLicenseKey();

        const { data: license, error } = await supabase
            .from('licenses')
            .insert({
                license_key: licenseKey,
                plan: 'basic',
                status: 'active',
                customer_email: userEmail,
                customer_name: userName,
                discord_id: userId,
                max_sales: 50,
                max_products: 3,
                created_at: new Date().toISOString(),
                expires_at: null, // Licença básica não expira
            })
            .select()
            .single();

        if (error) {
            console.error('Erro ao criar licença:', error);
            return res.status(500).json({ error: 'Falha ao criar licença' });
        }

        console.log(`✅ Licença básica criada para ${userName}: ${licenseKey}`);
        res.json({ licenseKey: license.license_key });

    } catch (error) {
        console.error('Erro ao criar licença gratuita:', error);
        antiCrashSystem.logError(`Erro ao criar licença gratuita: ${error.message}`);
        res.status(500).json({ error: 'Erro interno do servidor' });
    }
}));

// Rota para criar sessão de checkout Stripe
app.post('/api/stripe/create-checkout-session', authenticateHybrid, antiCrashSystem.wrapAsync(async (req, res) => {
    try {
        console.log('💳 Iniciando criação de sessão de checkout...');
        console.log('🔐 Usuário autenticado:', req.user ? `${req.user.username} (${req.user.id})` : 'NENHUM');
        console.log('📋 Dados recebidos:', {
            priceId: req.body.priceId,
            plan: req.body.plan,
            successUrl: req.body.successUrl ? '[PRESENTE]' : null,
            cancelUrl: req.body.cancelUrl ? '[PRESENTE]' : null
        });

        if (!req.user) {
            console.log('❌ Usuário não autenticado - req.user é null');
            return res.status(401).json({
                success: false,
                message: 'Autenticação requerida',
                code: 'AUTHENTICATION_REQUIRED'
            });
        }

        if (!stripe) {
            console.log('❌ Stripe não está configurado');
            return res.status(503).json({ error: 'Serviço de pagamento não disponível' });
        }

        const { priceId, plan, successUrl, cancelUrl } = req.body;
        const userId = req.user.id;
        const userEmail = req.user.email || `${req.user.username}@discord.user`;

        console.log('👤 Dados do usuário:', {
            userId,
            userEmail,
            username: req.user.username
        });

        if (!priceId || !plan) {
            console.log('❌ Campos obrigatórios ausentes');
            return res.status(400).json({ error: 'Campos obrigatórios ausentes' });
        }

        // URLs padrão se não fornecidas
        const defaultSuccessUrl = successUrl || `${req.protocol}://${req.get('host')}/success`;
        const defaultCancelUrl = cancelUrl || `${req.protocol}://${req.get('host')}/plans`;

        console.log('🔗 URLs configuradas:', {
            successUrl: defaultSuccessUrl,
            cancelUrl: defaultCancelUrl
        });

        // Criar sessão de checkout
        console.log('🔄 Criando sessão no Stripe...');
        const session = await stripe.checkout.sessions.create({
            payment_method_types: ['card'],
            line_items: [
                {
                    price: priceId,
                    quantity: 1,
                },
            ],
            mode: 'subscription',
            // Remover customer_email para permitir que o usuário digite o email
            // customer_email: userEmail,
            metadata: {
                plan: plan,
                discord_id: userId,
                discord_username: `${req.user.username}#${req.user.discriminator}`,
                discord_email: userEmail, // Salvar o email do Discord nos metadados para referência
            },
            success_url: `${defaultSuccessUrl}?session_id={CHECKOUT_SESSION_ID}`,
            cancel_url: defaultCancelUrl,
            allow_promotion_codes: true,
            billing_address_collection: 'required',
        });

        console.log(`💳 Sessão de checkout criada para ${req.user.username}: ${session.id}`);
        console.log(`🔗 URL do checkout: ${session.url}`);
        res.json({
            sessionId: session.id,
            url: session.url
        });

    } catch (error) {
        console.error('Erro ao criar sessão de checkout:', error);
        antiCrashSystem.logError(`Erro ao criar sessão de checkout: ${error.message}`);
        res.status(500).json({ error: 'Falha ao criar sessão de checkout' });
    }
}));

// Rota para obter informações da sessão Stripe
app.get('/api/stripe/session-info', authenticateHybrid, antiCrashSystem.wrapAsync(async (req, res) => {
    try {
        if (!supabase) {
            return res.status(503).json({ error: 'Serviço de licenças não disponível' });
        }
        const { session_id } = req.query;

        if (!session_id) {
            return res.status(400).json({ error: 'Session ID é obrigatório' });
        }

        // Buscar licença baseada no session_id do Stripe
        const { data: license, error } = await supabase
            .from('licenses')
            .select('license_key')
            .eq('stripe_session_id', session_id)
            .single();

        if (error || !license) {
            return res.status(404).json({ error: 'Licença não encontrada' });
        }

        res.json({ licenseKey: license.license_key });

    } catch (error) {
        console.error('Erro ao buscar informações da sessão:', error);
        antiCrashSystem.logError(`Erro ao buscar informações da sessão: ${error.message}`);
        res.status(500).json({ error: 'Erro interno do servidor' });
    }
}));

// Webhook do Stripe para processar pagamentos
app.post('/api/stripe/webhook', express.raw({type: 'application/json'}), antiCrashSystem.wrapAsync(async (req, res) => {
    console.log('🎯 Webhook do Stripe recebido');
    const sig = req.headers['stripe-signature'];
    const endpointSecret = process.env.STRIPE_WEBHOOK_SECRET;

    console.log('🔐 Verificando assinatura do webhook...');
    console.log('Signature presente:', !!sig);
    console.log('Endpoint secret configurado:', !!endpointSecret);

    let event;

    try {
        event = stripe.webhooks.constructEvent(req.body, sig, endpointSecret);
        console.log('✅ Assinatura do webhook verificada com sucesso');
        console.log('📋 Tipo do evento:', event.type);
    } catch (err) {
        console.error(`❌ Falha na verificação da assinatura do webhook:`, err.message);
        return res.status(400).send(`Webhook Error: ${err.message}`);
    }

    // Processar evento
    if (event.type === 'checkout.session.completed') {
        console.log('🛒 Processando checkout.session.completed');
        const session = event.data.object;

        console.log('📋 Dados da sessão:', {
            id: session.id,
            customer_email: session.customer_email,
            metadata: session.metadata
        });

        try {
            // Detectar plano baseado no metadata
            const plan = session.metadata.plan || 'pro';
            const licenseKey = generateLicenseKey();

            console.log(`🎯 Criando licença para plano: ${plan}`);
            console.log(`🔑 Chave da licença: ${licenseKey}`);

            // Salvar licença no sistema local
            const discordId = session.metadata.discord_id;
            if (discordId) {
                const userLicenseData = {
                    userId: discordId,
                    licenseKey: licenseKey,
                    plan: plan,
                    status: 'active',
                    createdAt: new Date().toISOString(),
                    stripeSessionId: session.id,
                    maxSales: -1, // Ilimitado
                    maxProducts: -1 // Ilimitado
                };

                const userData = database.getUser(discordId) || {};
                userData.license = userLicenseData;
                userData.lastLicenseUpdate = new Date().toISOString();
                database.saveUser(discordId, userData);

                console.log(`✅ Licença ${plan} criada via webhook: ${licenseKey} para usuário ${discordId}`);

                // Salvar também no Supabase na tabela user_licenses
                if (supabase) {
                    try {
                        const licenseData = {
                            user_id: discordId,
                            license_key: licenseKey,
                            plan_type: plan,
                            status: 'active',
                            stripe_session_id: session.id,
                            created_at: new Date().toISOString(),
                            max_sales: -1, // Ilimitado
                            max_products: -1 // Ilimitado
                        };

                        const { error: supabaseError } = await supabase
                            .from('user_licenses')
                            .insert(licenseData);

                        if (!supabaseError) {
                            console.log(`✅ Licença salva no Supabase via webhook`);
                        } else {
                            console.log(`⚠️ Erro ao salvar no Supabase via webhook: ${supabaseError.message}`);
                        }
                    } catch (supabaseError) {
                        console.log(`⚠️ Webhook erro ao conectar com Supabase: ${supabaseError.message}`);
                    }
                }
            } else {
                console.error('❌ Discord ID não encontrado nos metadados da sessão');
            }

        } catch (error) {
            console.error('Erro ao processar webhook:', error);
            antiCrashSystem.logError(`Erro ao processar webhook: ${error.message}`);
        }
    }

    res.json({received: true});
}));

// Rota para verificar licença
app.get('/api/licenses/verify/:licenseKey', antiCrashSystem.wrapAsync(async (req, res) => {
    try {
        const { licenseKey } = req.params;

        const { data: license, error } = await supabase
            .from('licenses')
            .select('*')
            .eq('license_key', licenseKey)
            .eq('status', 'active')
            .single();

        if (error || !license) {
            return res.status(404).json({ error: 'Licença não encontrada ou inválida' });
        }

        // Verificar se a licença expirou
        if (license.expires_at && new Date(license.expires_at) < new Date()) {
            return res.status(403).json({ error: 'Licença expirada' });
        }

        res.json({
            valid: true,
            plan: license.plan,
            maxSales: license.max_sales,
            maxProducts: license.max_products,
            createdAt: license.created_at,
            expiresAt: license.expires_at,
        });

    } catch (error) {
        console.error('Erro ao verificar licença:', error);
        antiCrashSystem.logError(`Erro ao verificar licença: ${error.message}`);
        res.status(500).json({ error: 'Erro interno do servidor' });
    }
}));

// Rota para processar pagamento manualmente (fallback se webhook falhar)
app.post('/api/stripe/process-payment', authenticateHybrid, antiCrashSystem.wrapAsync(async (req, res) => {
    try {
        if (!stripe || !supabase) {
            return res.status(503).json({ error: 'Serviços não disponíveis' });
        }

        const { session_id } = req.body;
        const userId = req.user.id;

        if (!session_id) {
            return res.status(400).json({ error: 'Session ID é obrigatório' });
        }

        console.log(`🔍 Processando pagamento manual para sessão: ${session_id}`);

        // Buscar sessão no Stripe
        const session = await stripe.checkout.sessions.retrieve(session_id);

        if (session.payment_status !== 'paid') {
            return res.status(400).json({ error: 'Pagamento não foi concluído' });
        }

        // Verificar se já existe licença para esta sessão no sistema local
        const userData = database.getUser(userId);
        if (userData && userData.license && userData.license.stripeSessionId === session_id) {
            console.log(`✅ Licença já existe para sessão: ${session_id}`);
            return res.json({
                success: true,
                licenseKey: userData.license.licenseKey,
                message: 'Licença já foi criada'
            });
        }

        // Criar licença
        const plan = session.metadata.plan || 'pro';
        const licenseKey = generateLicenseKey();

        console.log(`✅ Criando licença ${plan} para usuário ${userId}`);

        // Salvar APENAS no Supabase (não localmente)
        console.log(`✅ Licença criada manualmente: ${licenseKey} para usuário ${userId}`);

            // Salvar no Supabase usando uma tabela personalizada para licenças
            if (supabase) {
                try {
                    // Tentar salvar na tabela user_licenses (que vamos criar se não existir)
                    const licenseData = {
                        user_id: userId,
                        license_key: licenseKey,
                        plan_type: plan,
                        status: 'active',
                        stripe_session_id: session_id,
                        created_at: new Date().toISOString(),
                        max_sales: -1, // Ilimitado para ambos os planos
                        max_products: -1 // Ilimitado para ambos os planos
                    };

                    const { data: savedLicense, error: supabaseError } = await supabase
                        .from('user_licenses')
                        .insert(licenseData)
                        .select();

                    if (!supabaseError) {
                        console.log(`✅ Licença salva no Supabase na tabela user_licenses`);
                        console.log(`📋 Dados salvos:`, licenseData);

                        // Também adicionar na tabela licenses para validação do bot
                        try {
                            const licensesData = {
                                key: licenseKey,
                                customer_email: `${userId}@discord.user`,
                                plan: plan,
                                plan_name: plan === 'pro' ? 'Plano Pro' : 'Plano Basic',
                                stripe_session_id: session_id,
                                expires_at: null, // Sem expiração para licenças pagas
                                created_at: new Date().toISOString(),
                                updated_at: new Date().toISOString()
                            };

                            const { error: licensesError } = await supabase
                                .from('licenses')
                                .insert(licensesData);

                            if (!licensesError) {
                                console.log(`✅ Licença também adicionada na tabela licenses para validação do bot`);
                            } else {
                                console.log(`⚠️ Erro ao adicionar na tabela licenses: ${licensesError.message}`);
                            }
                        } catch (licensesError) {
                            console.log(`⚠️ Erro ao adicionar na tabela licenses: ${licensesError.message}`);
                        }
                    } else {
                        console.log(`⚠️ Erro ao salvar na tabela user_licenses: ${supabaseError.message}`);
                        console.log(`💡 Você precisa criar a tabela 'user_licenses' no Supabase com as colunas:`);
                        console.log(`   - user_id (text)`);
                        console.log(`   - license_key (text)`);
                        console.log(`   - plan_type (text)`);
                        console.log(`   - status (text)`);
                        console.log(`   - stripe_session_id (text)`);
                        console.log(`   - created_at (timestamp)`);
                        console.log(`   - max_sales (integer)`);
                        console.log(`   - max_products (integer)`);
                    }
                } catch (supabaseError) {
                    console.log(`⚠️ Erro ao conectar com Supabase: ${supabaseError.message}`);
                }
            }

        res.json({
            success: true,
            licenseKey: licenseKey,
            plan: plan
        });

    } catch (error) {
        console.error('Erro ao processar pagamento manual:', error);
        res.status(500).json({ error: 'Erro interno do servidor' });
    }
}));

// Rota para visualizar dados locais salvos (debug)
app.get('/api/debug/local-data', antiCrashSystem.wrapAsync(async (req, res) => {
    try {
        const fs = require('fs');
        const path = require('path');

        const dataPath = path.join(__dirname, 'data', 'users.json');

        if (fs.existsSync(dataPath)) {
            const rawData = fs.readFileSync(dataPath, 'utf8');
            const userData = JSON.parse(rawData);

            // Filtrar apenas dados de licenças para mostrar
            const licenseData = {};
            Object.keys(userData).forEach(userId => {
                if (userData[userId].license) {
                    licenseData[userId] = {
                        license: userData[userId].license,
                        lastLicenseUpdate: userData[userId].lastLicenseUpdate
                    };
                }
            });

            res.json({
                dataPath: dataPath,
                totalUsers: Object.keys(userData).length,
                usersWithLicense: Object.keys(licenseData).length,
                licenseData: licenseData
            });
        } else {
            res.json({
                dataPath: dataPath,
                exists: false,
                message: 'Arquivo de dados não existe ainda'
            });
        }
    } catch (error) {
        console.error('Erro ao ler dados locais:', error);
        res.status(500).json({ error: error.message });
    }
}));

// Rota para criar a tabela user_licenses no Supabase usando SQL direto
app.get('/api/setup/create-user-licenses-table', antiCrashSystem.wrapAsync(async (req, res) => {
    try {
        if (!supabase) {
            return res.status(503).json({ error: 'Supabase não disponível' });
        }

        console.log('🛠️ Criando tabela user_licenses no Supabase usando SQL...');

        // SQL para criar a tabela completa
        const createTableSQL = `
            -- Criar a tabela user_licenses
            CREATE TABLE IF NOT EXISTS user_licenses (
                id SERIAL PRIMARY KEY,
                user_id TEXT NOT NULL,
                license_key TEXT NOT NULL UNIQUE,
                plan_type TEXT NOT NULL,
                status TEXT NOT NULL DEFAULT 'active',
                stripe_session_id TEXT,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                expires_at TIMESTAMP WITH TIME ZONE,
                max_sales INTEGER DEFAULT -1,
                max_products INTEGER DEFAULT -1
            );

            -- Criar índices para performance
            CREATE INDEX IF NOT EXISTS idx_user_licenses_user_id ON user_licenses(user_id);
            CREATE INDEX IF NOT EXISTS idx_user_licenses_status ON user_licenses(status);
            CREATE INDEX IF NOT EXISTS idx_user_licenses_license_key ON user_licenses(license_key);

            -- Habilitar RLS (Row Level Security)
            ALTER TABLE user_licenses ENABLE ROW LEVEL SECURITY;

            -- Criar política para permitir todas as operações
            DROP POLICY IF EXISTS "Allow all operations for service" ON user_licenses;
            CREATE POLICY "Allow all operations for service"
            ON user_licenses FOR ALL
            USING (true)
            WITH CHECK (true);
        `;

        console.log('📋 Executando SQL para criar tabela...');

        // Tentar executar usando a API REST do Supabase
        const supabaseUrl = 'https://bfpgueewaqjnuuqgmxsr.supabase.co';
        const serviceKey = process.env.SUPABASE_SERVICE_KEY || process.env.SUPABASE_ANON_KEY;

        const response = await fetch(`${supabaseUrl}/rest/v1/rpc/exec_sql`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'apikey': serviceKey,
                'Authorization': `Bearer ${serviceKey}`,
                'Prefer': 'return=minimal'
            },
            body: JSON.stringify({
                sql: createTableSQL.trim()
            })
        });

        if (!response.ok) {
            console.error('❌ Erro na API REST:', response.status, response.statusText);

            // Tentar método alternativo - executar comandos separadamente
            console.log('🔄 Tentando método alternativo...');

            try {
                // Primeiro, tentar criar a tabela básica
                const { data: createData, error: createError } = await supabase
                    .from('user_licenses')
                    .insert({
                        user_id: 'setup_test',
                        license_key: 'SETUP_TEST_' + Date.now(),
                        plan_type: 'test',
                        status: 'test'
                    })
                    .select();

                if (createError && createError.message.includes('does not exist')) {
                    // A tabela não existe, precisamos criá-la manualmente
                    return res.status(500).json({
                        error: 'Tabela não existe e não foi possível criar automaticamente',
                        details: createError.message,
                        sqlToExecute: createTableSQL,
                        instructions: [
                            '1. Acesse: https://supabase.com/dashboard/project/bfpgueewaqjnuuqgmxsr/sql/new',
                            '2. Cole o SQL fornecido no campo "sqlToExecute"',
                            '3. Clique em "Run"',
                            '4. Tente novamente esta rota'
                        ]
                    });
                } else if (!createError) {
                    // A tabela existe, deletar o registro de teste
                    await supabase
                        .from('user_licenses')
                        .delete()
                        .eq('license_key', createData[0].license_key);

                    console.log('✅ Tabela user_licenses já existe e está funcionando');
                    return res.json({
                        success: true,
                        message: 'Tabela user_licenses já existe e está funcionando',
                        existing: true
                    });
                }
            } catch (altError) {
                console.error('❌ Erro no método alternativo:', altError);
            }

            return res.status(500).json({
                error: 'Não foi possível criar a tabela automaticamente',
                httpStatus: response.status,
                sqlToExecute: createTableSQL,
                instructions: [
                    '1. Acesse: https://supabase.com/dashboard/project/bfpgueewaqjnuuqgmxsr/sql/new',
                    '2. Cole o SQL fornecido no campo "sqlToExecute"',
                    '3. Clique em "Run"',
                    '4. Tente novamente esta rota'
                ]
            });
        }

        console.log('✅ SQL executado com sucesso via API REST');

        // Testar se a tabela foi criada corretamente
        const { data: testData, error: testError } = await supabase
            .from('user_licenses')
            .select('*')
            .limit(1);

        if (testError) {
            console.error('❌ Erro ao testar tabela criada:', testError);
            return res.status(500).json({
                error: 'Tabela criada mas não acessível',
                details: testError.message
            });
        }

        console.log('✅ Tabela user_licenses criada e testada com sucesso!');

        res.json({
            success: true,
            message: 'Tabela user_licenses criada com sucesso via SQL',
            method: 'API REST',
            testResult: 'Tabela acessível'
        });

    } catch (error) {
        console.error('❌ Erro geral no setup:', error);
        res.status(500).json({
            error: 'Erro no setup da tabela',
            details: error.message,
            sqlToExecute: `
                CREATE TABLE IF NOT EXISTS user_licenses (
                    id SERIAL PRIMARY KEY,
                    user_id TEXT NOT NULL,
                    license_key TEXT NOT NULL UNIQUE,
                    plan_type TEXT NOT NULL,
                    status TEXT NOT NULL DEFAULT 'active',
                    stripe_session_id TEXT,
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                    expires_at TIMESTAMP WITH TIME ZONE,
                    max_sales INTEGER DEFAULT -1,
                    max_products INTEGER DEFAULT -1
                );
            `,
            instructions: [
                '1. Acesse: https://supabase.com/dashboard/project/bfpgueewaqjnuuqgmxsr/sql/new',
                '2. Cole o SQL fornecido',
                '3. Clique em "Run"'
            ]
        });
    }
}));

// Rota para descobrir colunas da tabela licenses
app.get('/api/debug/discover-columns', antiCrashSystem.wrapAsync(async (req, res) => {
    try {
        if (!supabase) {
            return res.status(503).json({ error: 'Supabase não disponível' });
        }

        // Lista de possíveis nomes de colunas para testar
        const possibleColumns = [
            'id', 'license_id', 'key', 'license_key', 'license',
            'user_id', 'discord_id', 'user', 'owner_id',
            'plan', 'plan_type', 'type', 'tier',
            'status', 'active', 'is_active', 'enabled',
            'created_at', 'created', 'date_created',
            'updated_at', 'updated', 'date_updated',
            'expires_at', 'expiry', 'expiration',
            'max_sales', 'sales_limit', 'limit_sales',
            'max_products', 'products_limit', 'limit_products'
        ];

        const workingColumns = [];
        const failedColumns = [];

        // Testar cada coluna individualmente
        for (const column of possibleColumns) {
            try {
                const { data, error } = await supabase
                    .from('licenses')
                    .select(column)
                    .limit(1);

                if (!error) {
                    workingColumns.push(column);
                } else {
                    failedColumns.push({ column, error: error.message });
                }
            } catch (err) {
                failedColumns.push({ column, error: err.message });
            }
        }

        res.json({
            workingColumns: workingColumns,
            failedColumns: failedColumns,
            totalTested: possibleColumns.length,
            message: 'Descoberta de colunas da tabela licenses'
        });

    } catch (error) {
        console.error('Erro ao descobrir colunas:', error);
        res.status(500).json({ error: error.message });
    }
}));

// Rota para verificar status do token do bot
app.get('/api/bot-token-status/:userId', authenticateHybrid, antiCrashSystem.wrapAsync(async (req, res) => {
    try {
        const { userId } = req.params;
        const requestUserId = req.user.id;

        // Verificar se o usuário está tentando acessar seus próprios dados
        if (userId !== requestUserId) {
            return res.status(403).json({ error: 'Acesso negado' });
        }

        const userData = database.getUser(userId);
        if (!userData) {
            return res.json({ tokenValid: true, hasBot: false });
        }

        res.json({
            tokenValid: !userData.tokenInvalid,
            hasBot: !!userData.clientId,
            tokenInvalidAt: userData.tokenInvalidAt,
            botActive: userData.botActive
        });

    } catch (error) {
        console.error('Erro ao verificar status do token:', error);
        res.status(500).json({ error: 'Erro interno do servidor' });
    }
}));

// Rota para limpar flag de token inválido
app.post('/api/clear-token-invalid/:userId', authenticateHybrid, antiCrashSystem.wrapAsync(async (req, res) => {
    try {
        const { userId } = req.params;
        const requestUserId = req.user.id;

        // Verificar se o usuário está tentando acessar seus próprios dados
        if (userId !== requestUserId) {
            return res.status(403).json({ error: 'Acesso negado' });
        }

        const userData = database.getUser(userId);
        if (userData) {
            delete userData.tokenInvalid;
            delete userData.tokenInvalidAt;
            database.saveUser(userId, userData);
            console.log(`🔧 Flag de token inválido limpa para usuário ${userId}`);
        }

        res.json({ success: true });

    } catch (error) {
        console.error('Erro ao limpar flag de token inválido:', error);
        res.status(500).json({ error: 'Erro interno do servidor' });
    }
}));

// Rota para obter informações da licença do usuário
app.get('/api/user-license', authenticateHybrid, antiCrashSystem.wrapAsync(async (req, res) => {
    try {
        const userId = req.user.id;

        // Buscar licença no Supabase na tabela user_licenses
        if (supabase) {
            try {
                console.log(`🔍 Buscando licença no Supabase para usuário: ${userId}`);

                // Buscar dados do usuário na tabela discord_users que tem as datas corretas
                const { data: userData, error: userError } = await supabase
                    .from('discord_users')
                    .select('license_key, license_plan, license_activated_at, license_expires_at, license_validated')
                    .eq('discord_id', userId)
                    .single();

                console.log(`📊 Resultado da consulta discord_users:`, {
                    data: userData,
                    error: userError,
                    hasData: !!userData,
                    hasError: !!userError
                });

                if (!userError && userData && userData.license_key && userData.license_validated) {
                    console.log(`✅ Licença encontrada no Supabase para usuário ${userId}`);
                    console.log(`📋 Dados da licença:`, userData);

                    // Calcular tempo restante se houver data de expiração
                    let daysRemaining = null;
                    if (userData.license_expires_at) {
                        const expirationDate = new Date(userData.license_expires_at);
                        const now = new Date();
                        const timeDiff = expirationDate.getTime() - now.getTime();
                        daysRemaining = Math.floor(timeDiff / (1000 * 3600 * 24));
                    }

                    const responseData = {
                        hasLicense: true,
                        plan: userData.license_plan,
                        status: 'active',
                        licenseKey: userData.license_key,
                        maxSales: -1, // Sempre ilimitado
                        maxProducts: -1, // Sempre ilimitado
                        createdAt: userData.license_activated_at, // Usar data de ativação real
                        expiresAt: userData.license_expires_at,
                        daysRemaining: daysRemaining,
                        isExpired: userData.license_expires_at ? new Date(userData.license_expires_at) < new Date() : false
                    };

                    console.log(`📤 Enviando resposta da licença:`, responseData);
                    return res.json(responseData);
                } else {
                    console.log(`⚠️ Nenhuma licença ativada encontrada no Supabase para usuário ${userId}`);
                    console.log(`❌ Erro do Supabase:`, userError);
                }
            } catch (supabaseError) {
                console.log(`⚠️ Erro ao buscar no Supabase: ${supabaseError.message}`);
            }
        }

        // Nenhuma licença encontrada
        res.json({
            hasLicense: false,
            plan: 'free',
            status: 'inactive'
        });

    } catch (error) {
        console.error('Erro ao buscar licença do usuário:', error);
        antiCrashSystem.logError(`Erro ao buscar licença do usuário: ${error.message}`);
        res.status(500).json({ error: 'Erro interno do servidor' });
    }
}));

// ==================== FIM DAS ROTAS DE PAGAMENTO ====================

// Rota para iniciar autenticação Discord (DEVE VIR ANTES DO CATCH-ALL)
app.get('/auth/discord', (req, res) => {
    const plan = req.query.plan; // Capturar o plano se fornecido

    // URL de autorização do Discord
    const discordAuthUrl = new URL('https://discord.com/api/oauth2/authorize');
    discordAuthUrl.searchParams.set('client_id', DISCORD_CLIENT_ID);
    discordAuthUrl.searchParams.set('redirect_uri', plan ?
        (process.env.DISCORD_REDIRECT_URI_PLAN || 'http://localhost:3003/plan-callback') :
        DISCORD_REDIRECT_URI
    );
    discordAuthUrl.searchParams.set('response_type', 'code');
    discordAuthUrl.searchParams.set('scope', 'identify guilds.join guilds');

    // Se há um plano, adicionar como state para recuperar depois
    if (plan) {
        discordAuthUrl.searchParams.set('state', plan);
    }

    console.log(`🔗 Redirecionando para Discord OAuth${plan ? ` (plano: ${plan})` : ''}`);
    res.redirect(discordAuthUrl.toString());
});

// Rota catch-all - servir o React app
app.get('*', (req, res) => {
    res.sendFile(path.join(__dirname, 'discord-sales-oasis-main/dist/index.html'));
});

// Handler de erro seguro (deve ser o último middleware)
app.use(secureErrorHandler(process.env.NODE_ENV === 'development'));

// Inicializar o bot principal
const initializeMainBot = antiCrashSystem.wrapAsync(async function() {
    const success = await mainBot.start();
    if (!success) {
        console.error('Falha ao iniciar o bot principal');
        antiCrashSystem.logError('Falha ao iniciar o bot principal');
        // Não fazer exit aqui, deixar o anticrash decidir
        throw new Error('Falha ao iniciar o bot principal');
    }
});

// Inicializar sistema
const startSystem = antiCrashSystem.wrapAsync(async function() {
    try {
        // Iniciar bot principal
        await initializeMainBot();

        // Iniciar servidor web
        const PORT = process.env.PORT || 3003;
        const server = app.listen(PORT, async () => {
            console.log(`🚀 Servidor rodando em http://localhost:${PORT}`);

            // Carregar dados do Supabase primeiro e depois restaurar bots ativos
            setTimeout(async () => {
                try {
                    console.log('📂 Carregando dados do Supabase...');
                    await database.loadFromSupabase();

                    const allUsers = database.getAllUsers();
                    console.log(`📂 Carregados ${allUsers.size} usuários do Supabase`);

                    // Agora restaurar bots ativos
                    await botManager.restoreActiveBots();

                    // Inicializar bots SaaS centralizados
                    await initializeSaaSBots();
                } catch (error) {
                    console.error('❌ Erro na inicialização:', error);
                }
            }, 2000); // Aguardar 2 segundos para garantir que tudo está inicializado
        });

        // Adicionar handler de erro para o servidor
        server.on('error', (error) => {
            antiCrashSystem.logError(`Erro no servidor: ${error.message}`);
        });

    } catch (error) {
        console.error('Erro ao inicializar sistema:', error);
        antiCrashSystem.logError(`Erro ao inicializar sistema: ${error.message}`);
        // Não fazer exit aqui, deixar o anticrash decidir
        throw error;
    }
});

// Iniciar o sistema
startSystem();
