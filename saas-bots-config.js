// Configuração dos bots centralizados do sistema SaaS
const SAAS_BOTS_CONFIG = {
  basic: {
    id: 'nodex-basic',
    name: 'Nodex Bot Basic',
    token: process.env.BASIC_BOT_TOKEN, // Token do bot básico
    clientId: process.env.BASIC_BOT_CLIENT_ID,
    plan: 'basic',
    features: [
      'Sistema de vendas básico',
      'Até 10 produtos por categoria',
      'Pagamentos via PIX Manual',
      'Logs básicos',
      'Suporte por email',
      'Sistema de tickets básico'
    ],
    limits: {
      products: 50,
      categories: 5,
      sales_per_month: 100
    }
  },
  pro: {
    id: 'nodex-pro',
    name: 'Nodex Bot Pro',
    token: process.env.PRO_BOT_TOKEN, // Token do bot pro
    clientId: process.env.PRO_BOT_CLIENT_ID,
    plan: 'pro',
    features: [
      'Sistema de vendas avançado',
      'Produtos ilimitados',
      'Mercado Pago + PIX Manual',
      'Sistema de tickets completo',
      'Anti-fake avançado',
      'Logs completos',
      'Automações avançadas',
      'Suporte prioritário',
      'Relatórios detalhados'
    ],
    limits: {
      products: -1, // Ilimitado
      categories: -1, // Ilimitado
      sales_per_month: -1 // Ilimitado
    }
  }
};

// URLs de convite dos bots (substitua pelos IDs reais dos seus bots)
const BOT_INVITE_URLS = {
  basic: `https://discord.com/api/oauth2/authorize?client_id=${process.env.BASIC_BOT_CLIENT_ID}&permissions=8&scope=bot%20applications.commands`,
  pro: `https://discord.com/api/oauth2/authorize?client_id=${process.env.PRO_BOT_CLIENT_ID}&permissions=8&scope=bot%20applications.commands`
};

module.exports = {
  SAAS_BOTS_CONFIG,
  BOT_INVITE_URLS
};
