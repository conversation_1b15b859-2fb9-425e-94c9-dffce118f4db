const { createClient } = require('@supabase/supabase-js');

class SupabaseDatabase {
    constructor() {
        // Configuração do Supabase
        this.supabaseUrl = process.env.SUPABASE_URL;
        this.supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
        
        if (!this.supabaseUrl || !this.supabaseKey) {
            throw new Error('Configurações do Supabase não encontradas no .env');
        }
        
        this.supabase = createClient(this.supabaseUrl, this.supabaseKey);
        // console.log('✅ Supabase Database inicializado');
        
        // Manter compatibilidade com o sistema antigo
        this.users = new Map();
        this.userBots = new Map();
        this.pendingConfigurations = new Map();
        
        // Carregar dados do Supabase para memória (cache) - será chamado manualmente
        // this.loadFromSupabase(); // Removido para evitar problemas de async no construtor
    }

    async loadFromSupabase() {
        try {
            const { data: users, error } = await this.supabase
                .from('discord_users')
                .select('*');
                
            if (error) {
                console.error('❌ Erro ao carregar usuários do Supabase:', error);
                return;
            }
            
            // Carregar para o Map para compatibilidade, transformando nomes de campos
            for (const user of users) {
                // Transformar campos do Supabase (snake_case) para o formato esperado (camelCase)
                const transformedUser = {
                    ...user,
                    // Manter campos originais para compatibilidade
                    sessionToken: user.session_token,
                    jwtToken: user.jwt_token,
                    urlToken: user.url_token,
                    accessToken: user.access_token,
                    clientId: user.client_id,
                    botToken: user.bot_token,
                    token: user.bot_token, // Compatibilidade com código que usa userData.token
                    botActive: user.bot_active,
                    botStartedAt: user.bot_started_at,
                    botStoppedAt: user.bot_stopped_at,
                    loginCount: user.login_count,
                    createdAt: user.created_at,
                    updatedAt: user.updated_at,
                    lastLogin: user.last_login,
                    // Campos importantes para o sistema de boas-vindas
                    registered: user.registered,
                    guildId: user.guild_id,
                    // Dados da licença
                    licenseKey: user.license_key,
                    licenseValidated: user.license_validated,
                    licensePlan: user.license_plan,
                    licenseActivatedAt: user.license_activated_at,
                    licenseExpiresAt: user.license_expires_at,
                    licenseExpiry: user.license_expires_at, // Manter compatibilidade
                    // Dados do Mercado Pago
                    mercadoPagoAccessToken: user.mercado_pago_access_token,
                    mercadoPagoClientSecret: user.mercado_pago_client_secret,
                    mercadoPagoAccountInfo: user.mercado_pago_account_info,
                    mercadoPagoConfirmed: user.mercado_pago_confirmed,
                    mercadoPagoConfirmedAt: user.mercado_pago_confirmed_at
                };

                // Carregar configurações do bot para este usuário
                const botConfigs = await this.getAllBotConfigs(user.discord_id);
                const userWithConfigs = { ...transformedUser, ...botConfigs };

                this.users.set(user.discord_id, userWithConfigs);
            }
            
            // console.log(`📂 Carregados ${users.length} usuários do Supabase`);
        } catch (error) {
            console.error('❌ Erro ao conectar com Supabase:', error);
        }
    }

    async saveUser(userId, userData) {
        try {
            // Atualizar cache local
            this.users.set(userId, userData);

            // console.log(`💾 Salvando usuário ${userId} no Supabase:`, {
            //     token: userData.token ? '[PRESENTE]' : null,
            //     botToken: userData.botToken ? '[PRESENTE]' : null,
            //     clientId: userData.clientId || null,
            //     botActive: userData.botActive || false
            // });

            // Preparar dados para o Supabase
            const supabaseData = {
                id: userId,
                discord_id: userId,
                username: userData.username || '',
                discriminator: userData.discriminator || '0',
                avatar: userData.avatar || null,
                email: userData.email || null,
                access_token: userData.accessToken || null,
                jwt_token: userData.jwtToken || null,
                session_token: userData.sessionToken || null,
                url_token: userData.urlToken || null,
                client_id: userData.clientId || null,
                bot_token: userData.token || userData.botToken || null,
                bot_active: userData.botActive || false,
                bot_started_at: userData.botStartedAt || null,
                bot_stopped_at: userData.botStoppedAt || null,
                login_count: userData.loginCount || 0,
                updated_at: new Date().toISOString(),
                last_login: userData.lastLogin || new Date().toISOString(),
                // Campos importantes para o sistema de boas-vindas
                registered: userData.registered || false,
                guild_id: userData.guildId || null,
                // Dados da licença
                license_key: userData.licenseKey || null,
                license_validated: userData.licenseValidated || false,
                license_plan: userData.licensePlan || null,
                license_activated_at: userData.licenseActivatedAt || null,
                license_expires_at: userData.licenseExpiresAt || null,
                // Dados do Mercado Pago
                mercado_pago_access_token: userData.mercadoPagoAccessToken || null,
                mercado_pago_client_secret: userData.mercadoPagoClientSecret || null,
                mercado_pago_account_info: userData.mercadoPagoAccountInfo || null,
                mercado_pago_confirmed: userData.mercadoPagoConfirmed || false,
                mercado_pago_confirmed_at: userData.mercadoPagoConfirmedAt || null
            };

            // Salvar no Supabase
            const { data, error } = await this.supabase
                .from('discord_users')
                .upsert(supabaseData, { 
                    onConflict: 'discord_id',
                    returning: 'minimal'
                });

            if (error) {
                console.error('❌ Erro ao salvar usuário no Supabase:', error);
                return false;
            }

            // Atualizar cache local
            this.users.set(userId, userData);

            // Salvar configurações do bot se existirem
            if (userData.products || userData.produtos) {
                await this.saveBotConfig(userId, 'products', userData.products || userData.produtos);
            }
            if (userData.categories || userData.categorias) {
                await this.saveBotConfig(userId, 'categories', userData.categories || userData.categorias);
            }
            if (userData.stock || userData.estoque) {
                await this.saveBotConfig(userId, 'stock', userData.stock || userData.estoque);
            }
            if (userData.roles || userData.cargos) {
                await this.saveBotConfig(userId, 'roles', userData.roles || userData.cargos);
            }
            if (userData.channels || userData.canais) {
                await this.saveBotConfig(userId, 'channels', userData.channels || userData.canais);
            }
            if (userData.settings || userData.configuracoes) {
                await this.saveBotConfig(userId, 'settings', userData.settings || userData.configuracoes);
            }
            if (userData.sales || userData.vendas) {
                await this.saveBotConfig(userId, 'sales', userData.sales || userData.vendas);
            }
            if (userData.logs) {
                await this.saveBotConfig(userId, 'logs', userData.logs);
            }
            if (userData.tickets) {
                await this.saveBotConfig(userId, 'tickets', userData.tickets);
            }
            if (userData.permissions || userData.permissoes) {
                await this.saveBotConfig(userId, 'permissions', userData.permissions || userData.permissoes);
            }
            if (userData.boasVindas) {
                await this.saveBotConfig(userId, 'welcome', userData.boasVindas);
            }
            if (userData.antiFake) {
                await this.saveBotConfig(userId, 'antifake', userData.antiFake);
            }
            if (userData.mercadoPago) {
                await this.saveBotConfig(userId, 'mercadopago', userData.mercadoPago);
            }
            if (userData.bancosBloqueados) {
                await this.saveBotConfig(userId, 'blocked_banks', userData.bancosBloqueados);
            }

            // console.log(`💾 Usuário ${userId} salvo no Supabase com bot_token:`, supabaseData.bot_token ? '[PRESENTE]' : null);
            return true;
        } catch (error) {
            console.error('❌ Erro ao salvar usuário:', error);
            return false;
        }
    }

    // Método síncrono para compatibilidade com código existente
    getUser(userId) {
        return this.users.get(userId) || null;
    }

    // Método assíncrono para carregar dados completos do Supabase
    async getUserAsync(userId) {
        // Primeiro, tentar buscar do cache local
        let userData = this.users.get(userId);

        if (!userData) {
            // Se não estiver no cache, buscar do Supabase
            try {
                const { data, error } = await this.supabase
                    .from('discord_users')
                    .select('*')
                    .eq('discord_id', userId)
                    .single();

                if (error) {
                    if (error.code !== 'PGRST116') {
                        console.error(`❌ Erro ao buscar usuário ${userId} do Supabase:`, error);
                    }
                    return null;
                }

                // Transformar dados do Supabase para o formato esperado
                userData = {
                    ...data,
                    sessionToken: data.session_token,
                    jwtToken: data.jwt_token,
                    urlToken: data.url_token,
                    accessToken: data.access_token,
                    clientId: data.client_id,
                    botToken: data.bot_token,
                    token: data.bot_token,
                    botActive: data.bot_active,
                    botStartedAt: data.bot_started_at,
                    botStoppedAt: data.bot_stopped_at,
                    loginCount: data.login_count,
                    createdAt: data.created_at,
                    updatedAt: data.updated_at,
                    lastLogin: data.last_login,
                    // Campos importantes para o sistema de boas-vindas
                    registered: data.registered,
                    guildId: data.guild_id,
                    licenseKey: data.license_key,
                    licenseValidated: data.license_validated,
                    licensePlan: data.license_plan,
                    licenseActivatedAt: data.license_activated_at,
                    licenseExpiresAt: data.license_expires_at,
                    mercadoPagoAccessToken: data.mercado_pago_access_token,
                    mercadoPagoClientSecret: data.mercado_pago_client_secret,
                    mercadoPagoAccountInfo: data.mercado_pago_account_info,
                    mercadoPagoConfirmed: data.mercado_pago_confirmed,
                    mercadoPagoConfirmedAt: data.mercado_pago_confirmed_at
                };

                // Carregar configurações do bot
                const botConfigs = await this.getAllBotConfigs(userId);
                userData = { ...userData, ...botConfigs };

                // Adicionar ao cache
                this.users.set(userId, userData);
            } catch (error) {
                console.error(`❌ Erro ao buscar usuário ${userId}:`, error);
                return null;
            }
        }

        return userData;
    }

    getAllUsers() {
        return this.users;
    }

    saveUserBot(userId, botInstance) {
        this.userBots.set(userId, botInstance);
        console.log(`🤖 Bot salvo para usuário ${userId}`);
    }

    getUserBot(userId) {
        return this.userBots.get(userId) || null;
    }

    deleteUserBot(userId) {
        const bot = this.userBots.get(userId);
        if (bot) {
            this.userBots.delete(userId);
            console.log(`🗑️ Bot removido para usuário ${userId}`);
        }
    }

    // Estatísticas
    getStats() {
        return {
            totalUsers: this.users.size,
            activeBots: this.userBots.size,
            pendingConfigs: this.pendingConfigurations.size
        };
    }

    async deleteUser(userId) {
        try {
            const { error } = await this.supabase
                .from('discord_users')
                .delete()
                .eq('discord_id', userId);

            if (error) {
                console.error('❌ Erro ao deletar usuário do Supabase:', error);
                return false;
            }

            // Remover do cache local
            this.users.delete(userId);
            
            console.log(`🗑️ Usuário ${userId} deletado do Supabase`);
            return true;
        } catch (error) {
            console.error('❌ Erro ao deletar usuário:', error);
            return false;
        }
    }

    getStats() {
        return {
            totalUsers: this.users.size,
            activeUsers: Array.from(this.users.values()).filter(user => user.botActive).length,
            totalBots: this.userBots.size,
            pendingConfigs: this.pendingConfigurations.size
        };
    }

    // Métodos de compatibilidade com o sistema antigo
    savePendingConfiguration(userId, configData) {
        this.pendingConfigurations.set(userId, configData);
        console.log(`📋 Configuração pendente salva para usuário ${userId}`);
    }

    getPendingConfiguration(userId) {
        return this.pendingConfigurations.get(userId) || null;
    }

    deletePendingConfiguration(userId) {
        this.pendingConfigurations.delete(userId);
        console.log(`🗑️ Configuração pendente removida para usuário ${userId}`);
    }

    // Configurações do Bot
    async saveBotConfig(userId, configType, configData) {
        try {
            const { data, error } = await this.supabase
                .from('bot_configs')
                .upsert({
                    discord_id: userId,
                    config_type: configType,
                    config_data: configData,
                    updated_at: new Date().toISOString()
                }, {
                    onConflict: 'discord_id,config_type',
                    returning: 'minimal'
                });

            if (error) {
                console.error(`❌ Erro ao salvar configuração ${configType} para usuário ${userId}:`, error);
                return false;
            }

            console.log(`💾 Configuração ${configType} salva para usuário ${userId}`);
            return true;
        } catch (error) {
            console.error(`❌ Erro ao salvar configuração ${configType}:`, error);
            return false;
        }
    }

    async getBotConfig(userId, configType) {
        try {
            const { data, error } = await this.supabase
                .from('bot_configs')
                .select('config_data')
                .eq('discord_id', userId)
                .eq('config_type', configType)
                .single();

            if (error) {
                if (error.code === 'PGRST116') {
                    // Nenhum registro encontrado
                    return null;
                }
                console.error(`❌ Erro ao buscar configuração ${configType} para usuário ${userId}:`, error);
                return null;
            }

            return data?.config_data || null;
        } catch (error) {
            console.error(`❌ Erro ao buscar configuração ${configType}:`, error);
            return null;
        }
    }

    async getAllBotConfigs(userId) {
        try {
            const { data, error } = await this.supabase
                .from('bot_configs')
                .select('config_type, config_data')
                .eq('discord_id', userId);

            if (error) {
                console.error(`❌ Erro ao buscar todas as configurações para usuário ${userId}:`, error);
                return {};
            }

            // Converter array para objeto com mapeamento reverso
            const configs = {};
            data.forEach(config => {
                // Mapear nomes do banco para nomes esperados pelo código
                switch(config.config_type) {
                    case 'welcome':
                        configs['boasVindas'] = config.config_data;
                        break;
                    case 'categories':
                        configs['categorias'] = config.config_data;
                        configs['categories'] = config.config_data; // Manter ambos para compatibilidade
                        break;
                    case 'products':
                        configs['produtos'] = config.config_data;
                        configs['products'] = config.config_data; // Manter ambos para compatibilidade
                        break;
                    case 'stock':
                        configs['estoque'] = config.config_data;
                        configs['stock'] = config.config_data; // Manter ambos para compatibilidade
                        break;
                    case 'roles':
                        configs['cargos'] = config.config_data;
                        configs['roles'] = config.config_data; // Manter ambos para compatibilidade
                        break;
                    case 'channels':
                        configs['canais'] = config.config_data;
                        configs['channels'] = config.config_data; // Manter ambos para compatibilidade
                        break;
                    case 'settings':
                        configs['configuracoes'] = config.config_data;
                        configs['settings'] = config.config_data; // Manter ambos para compatibilidade
                        break;
                    case 'sales':
                        configs['vendas'] = config.config_data;
                        configs['sales'] = config.config_data; // Manter ambos para compatibilidade
                        break;
                    case 'permissions':
                        configs['permissoes'] = config.config_data;
                        configs['permissions'] = config.config_data; // Manter ambos para compatibilidade
                        break;
                    case 'antifake':
                        configs['antiFake'] = config.config_data;
                        break;
                    case 'mercadopago':
                        configs['mercadoPago'] = config.config_data;
                        break;
                    case 'blocked_banks':
                        configs['bancosBloqueados'] = config.config_data;
                        break;
                    default:
                        configs[config.config_type] = config.config_data;
                        break;
                }
            });

            return configs;
        } catch (error) {
            console.error(`❌ Erro ao buscar todas as configurações:`, error);
            return {};
        }
    }

    async deleteBotConfig(userId, configType) {
        try {
            const { error } = await this.supabase
                .from('bot_configs')
                .delete()
                .eq('discord_id', userId)
                .eq('config_type', configType);

            if (error) {
                console.error(`❌ Erro ao deletar configuração ${configType} para usuário ${userId}:`, error);
                return false;
            }

            console.log(`🗑️ Configuração ${configType} deletada para usuário ${userId}`);
            return true;
        } catch (error) {
            console.error(`❌ Erro ao deletar configuração ${configType}:`, error);
            return false;
        }
    }

    // Método para sincronizar dados locais com Supabase
    async syncToSupabase() {
        console.log('🔄 Sincronizando dados locais com Supabase...');

        for (const [userId, userData] of this.users) {
            await this.saveUser(userId, userData);
        }

        console.log('✅ Sincronização concluída');
    }
}

module.exports = SupabaseDatabase;
