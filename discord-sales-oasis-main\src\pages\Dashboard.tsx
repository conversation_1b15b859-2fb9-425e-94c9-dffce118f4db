import { useEffect, useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from "@/components/ui/alert-dialog";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { toast } from "sonner";
import { Bot, Plus, Settings, Play, Pause, Trash2, ExternalLink, Users, ShoppingCart, TrendingUp, LogOut, RotateCcw, AlertTriangle, Home, X } from "lucide-react";
import { useNavigate, useSearchParams, useParams } from "react-router-dom";
import { saveTokens, getTokens, isTokenValid, checkAndRefreshAuth, logout, authenticatedFetch } from "@/utils/auth";

interface BotData {
  id: string;
  name: string;
  clientId: string;
  status: 'online' | 'offline' | 'error';
  servers: number;
  sales: number;
  lastSale: string;
  plan: string;
  cluster: string;
  tariff: number;
  avatar?: string;
}

interface LicenseData {
  hasLicense: boolean;
  plan: string;
  status: string;
  licenseKey?: string;
  maxSales?: number;
  maxProducts?: number;
  createdAt?: string;
  expiresAt?: string;
  daysRemaining?: number;
  isExpired?: boolean;
}

const Dashboard = () => {
  const [bots, setBots] = useState<BotData[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [user, setUser] = useState<any>(null);
  const [userFullData, setUserFullData] = useState<any>(null);
  const [license, setLicense] = useState<LicenseData | null>(null);
  const [restartingBots, setRestartingBots] = useState<Set<string>>(new Set());
  const [tokenStatus, setTokenStatus] = useState<any>(null);
  const [showLicenseDetails, setShowLicenseDetails] = useState(false);
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const { urlToken } = useParams();

  useEffect(() => {
    const initializeAuth = async () => {
      // Pegar tokens da URL (vindo do login)
      const sessionToken = searchParams.get('token');
      const jwtToken = searchParams.get('jwt');

      console.log('Dashboard useEffect - sessionToken da URL:', sessionToken);
      console.log('Dashboard useEffect - jwtToken da URL:', jwtToken ? 'JWT_PRESENT' : 'NO_JWT');
      console.log('Dashboard useEffect - urlToken da URL:', urlToken);

      if (sessionToken || jwtToken) {
        // Usuário logado via OAuth - salvar tokens
        const tokens = {
          sessionToken: sessionToken || undefined,
          jwtToken: jwtToken || undefined,
          urlToken: urlToken || undefined
        };

        saveTokens(tokens);
        console.log('Tokens salvos no localStorage');

        // Pular verificação de refresh já que acabamos de receber tokens válidos do servidor
        try {
          await loadUserInfo();

          // Limpar tokens da URL após salvar, mantendo urlToken se existir
          if (urlToken) {
            navigate(`/dashboard/${urlToken}`, { replace: true });
          } else {
            // Se não temos urlToken na URL, buscar dos dados do usuário
            await redirectToCorrectUrl();
          }
        } catch (error) {
          console.error('Erro ao carregar dados do usuário:', error);
          setIsLoading(false);
          window.location.href = '/auth/discord';
        }
      } else {
        // Tentar usar tokens salvos
        const savedTokens = getTokens();
        console.log('Tokens do localStorage:', {
          hasJWT: !!savedTokens.jwtToken,
          hasSession: !!savedTokens.sessionToken,
          hasUrl: !!savedTokens.urlToken
        });

        if (savedTokens.jwtToken || savedTokens.sessionToken) {
          // Verificar e renovar autenticação se necessário
          const authValid = await checkAndRefreshAuth();

          if (authValid) {
            // Se temos urlToken salvo mas não estamos na URL correta, redirecionar
            if (savedTokens.urlToken && !urlToken) {
              navigate(`/dashboard/${savedTokens.urlToken}`, { replace: true });
              return;
            }

            // Carregar dados do usuário
            await loadUserInfo();
          } else {
            console.log('Autenticação inválida, redirecionando para autenticação Discord');
            setIsLoading(false);
            window.location.href = '/auth/discord';
          }
        } else {
          // Nenhum token encontrado - redirecionar diretamente para autenticação Discord
          console.log('Nenhum token encontrado, redirecionando para autenticação Discord');
          setIsLoading(false);
          window.location.href = '/auth/discord';
        }
      }
    };

    initializeAuth();
  }, [searchParams, navigate, urlToken]);

  const redirectToCorrectUrl = async () => {
    try {
      const response = await authenticatedFetch('/api/user-full');
      if (response.ok) {
        const fullUserData = await response.json();
        if (fullUserData.urlToken) {
          saveTokens({ urlToken: fullUserData.urlToken });
          navigate(`/dashboard/${fullUserData.urlToken}`, { replace: true });
          return;
        }
      }
    } catch (error) {
      console.error('Erro ao buscar dados completos:', error);
    }
  };

  const loadUserInfo = async () => {
    try {
      console.log('Carregando informações do usuário...');
      const response = await authenticatedFetch('/api/user-info');
      console.log('Resposta da API user-info:', response.status, response.statusText);

      if (response.ok) {
        const userData = await response.json();
        console.log('Dados do usuário recebidos:', userData);
        setUser(userData);

        // Buscar dados completos do usuário (incluindo urlToken)
        const fullResponse = await authenticatedFetch('/api/user-full');
        if (fullResponse.ok) {
          const fullUserData = await fullResponse.json();
          setUserFullData(fullUserData);

          // Salvar urlToken se não estiver salvo
          if (fullUserData.urlToken) {
            const tokens = getTokens();
            if (!tokens.urlToken) {
              saveTokens({ ...tokens, urlToken: fullUserData.urlToken });
            }
          }
        }

        loadUserBots(userData.id);
        loadUserLicense();
        loadTokenStatus();
      } else {
        // Token inválido, fazer logout
        console.log('Token inválido, fazendo logout');
        setIsLoading(false);
        logout();
      }
    } catch (error) {
      console.error('Erro ao verificar autenticação:', error);
      setIsLoading(false);
      logout();
    }
  };

  const loadTokenStatus = async () => {
    try {
      if (!user?.id) return;

      const response = await authenticatedFetch(`/api/bot-token-status/${user.id}`);
      if (response.ok) {
        const status = await response.json();
        setTokenStatus(status);
        console.log('🔍 Status do token:', status);
      }
    } catch (error) {
      console.error('❌ Erro ao verificar status do token:', error);
    }
  };

  const clearTokenInvalid = async () => {
    try {
      if (!user?.id) return;

      const response = await authenticatedFetch(`/api/clear-token-invalid/${user.id}`, {
        method: 'POST'
      });

      if (response.ok) {
        console.log('✅ Flag de token inválido limpa');
        await loadTokenStatus(); // Recarregar status
      }
    } catch (error) {
      console.error('❌ Erro ao limpar flag de token inválido:', error);
    }
  };

  const loadUserLicense = async () => {
    try {
      console.log('🔍 Carregando informações da licença...');

      // Tentar múltiplas vezes se necessário
      let response;
      let attempts = 0;
      const maxAttempts = 3;

      while (attempts < maxAttempts) {
        attempts++;
        console.log(`📡 Tentativa ${attempts} de carregar licença...`);

        response = await authenticatedFetch(`/api/user-license?t=${Date.now()}`);

        if (response.ok) {
          break;
        } else if (attempts < maxAttempts) {
          console.log(`⚠️ Tentativa ${attempts} falhou, tentando novamente...`);
          await new Promise(resolve => setTimeout(resolve, 1000)); // Aguardar 1 segundo
        }
      }

      if (response && response.ok) {
        const licenseData = await response.json();
        console.log('✅ Dados da licença recebidos:', licenseData);
        console.log('🔍 Tipo do plano recebido:', typeof licenseData.plan, licenseData.plan);
        console.log('🔍 hasLicense:', licenseData.hasLicense);
        console.log('🔍 daysRemaining recebido:', licenseData.daysRemaining, typeof licenseData.daysRemaining);
        setLicense(licenseData);
        console.log('✅ Estado da licença atualizado');
      } else {
        console.log('❌ Erro ao carregar licença após todas as tentativas');
        console.log('❌ Status da resposta:', response?.status);
        setLicense({
          hasLicense: false,
          plan: 'free',
          status: 'inactive'
        });
      }
    } catch (error) {
      console.error('❌ Erro ao carregar licença:', error);
      setLicense({
        hasLicense: false,
        plan: 'free',
        status: 'inactive'
      });
    }
  };

  const loadUserBots = async (userId: string) => {
    try {
      console.log('Carregando bots para usuário:', userId);
      const response = await fetch(`/api/user-bots/${userId}`);
      console.log('Resposta da API user-bots:', response.status, response.statusText);

      if (response.ok) {
        const data = await response.json();
        console.log('Dados dos bots recebidos:', data);
        setBots(data.bots || []);
      } else {
        console.error('Erro ao carregar bots - resposta não OK:', response.status);
      }
    } catch (error) {
      console.error('Erro ao carregar bots:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleBotAction = async (botId: string, action: 'start' | 'stop' | 'restart' | 'delete') => {
    const actionMessages = {
      start: { loading: 'Iniciando bot...', success: 'Bot iniciado com sucesso!', error: 'Erro ao iniciar bot' },
      stop: { loading: 'Parando bot...', success: 'Bot parado com sucesso!', error: 'Erro ao parar bot' },
      restart: { loading: 'Reiniciando bot...', success: 'Bot reiniciado com sucesso!', error: 'Erro ao reiniciar bot' },
      delete: { loading: 'Removendo bot...', success: 'Bot removido com sucesso!', error: 'Erro ao remover bot' }
    };

    const messages = actionMessages[action];

    // Atualizar status imediatamente para feedback visual
    setBots(prevBots =>
      prevBots.map(bot =>
        bot.id === botId
          ? {
              ...bot,
              status: action === 'start' ? 'starting' :
                     action === 'stop' ? 'stopping' :
                     action === 'restart' ? 'restarting' : bot.status
            }
          : bot
      )
    );

    // Adicionar animação para restart
    if (action === 'restart') {
      setRestartingBots(prev => new Set([...prev, botId]));
    }

    // Mostrar toast de loading
    const loadingToast = toast.loading(messages.loading);

    try {
      const response = await fetch(`/api/bot-action`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ botId, action })
      });

      const result = await response.json();

      if (response.ok && result.success) {
        toast.success(messages.success, { id: loadingToast });

        // Remover animação após sucesso
        if (action === 'restart') {
          setTimeout(() => {
            setRestartingBots(prev => {
              const newSet = new Set(prev);
              newSet.delete(botId);
              return newSet;
            });
          }, 2000);
        }

        // Recarregar lista de bots após um pequeno delay para dar tempo da ação ser processada
        setTimeout(() => {
          if (user) {
            loadUserBots(user.id);
          }
        }, action === 'restart' ? 3000 : 1500); // Mais tempo para restart
      } else {
        toast.error(result.message || messages.error, { id: loadingToast });
        // Reverter status em caso de erro
        if (user) {
          loadUserBots(user.id);
        }
        // Remover animação em caso de erro
        if (action === 'restart') {
          setRestartingBots(prev => {
            const newSet = new Set(prev);
            newSet.delete(botId);
            return newSet;
          });
        }
      }
    } catch (error) {
      console.error('Erro ao executar ação:', error);
      toast.error(messages.error, { id: loadingToast });
      // Reverter status em caso de erro
      if (user) {
        loadUserBots(user.id);
      }
      // Remover animação em caso de erro
      if (action === 'restart') {
        setRestartingBots(prev => {
          const newSet = new Set(prev);
          newSet.delete(botId);
          return newSet;
        });
      }
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'online': return 'text-green-400';
      case 'offline': return 'text-gray-400';
      case 'error': return 'text-red-400';
      case 'starting': return 'text-yellow-400';
      case 'stopping': return 'text-orange-400';
      case 'restarting': return 'text-blue-400';
      default: return 'text-gray-400';
    }
  };

  const getStatusDot = (status: string) => {
    switch (status) {
      case 'online': return 'bg-green-500';
      case 'offline': return 'bg-gray-500';
      case 'error': return 'bg-red-500';
      case 'starting': return 'bg-yellow-500 animate-pulse';
      case 'stopping': return 'bg-orange-500 animate-pulse';
      case 'restarting': return 'bg-blue-500 animate-pulse';
      default: return 'bg-gray-500';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'online': return 'Online';
      case 'offline': return 'Offline';
      case 'error': return 'Erro';
      case 'starting': return 'Iniciando...';
      case 'stopping': return 'Parando...';
      case 'restarting': return 'Reiniciando...';
      default: return 'Desconhecido';
    }
  };

  if (isLoading) {
    return (
      <div className="dark min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <Bot className="w-12 h-12 text-primary mx-auto mb-4 animate-pulse" />
          <p className="text-muted-foreground">Carregando dashboard...</p>
        </div>
      </div>
    );
  }

  return (
    <TooltipProvider>
      <div className="dark min-h-screen bg-background text-foreground relative overflow-hidden">
        {/* Background Pattern */}
        <div className="absolute inset-0 z-[-10] size-full opacity-60 dark:opacity-40"
             style={{
               backgroundImage: 'radial-gradient(hsl(var(--border)/0.3) 1px, transparent 1px)',
               backgroundSize: '24px 24px',
               maskImage: 'radial-gradient(ellipse at center, var(--background), transparent)'
             }} />

        <div className="container mx-auto px-4 py-8 relative z-10">
        {/* Header with glassmorphism */}
        <div className="flex items-center justify-between mb-8 p-6 backdrop-blur-xl rounded-2xl border border-white/10 bg-white/5 shadow-lg">
          <div>
            <h1 className="text-4xl font-bold bg-gradient-to-r from-white to-white/80 bg-clip-text text-transparent">
              Dashboard
            </h1>
            <p className="text-muted-foreground text-lg mt-2">
              Bem-vindo, <strong>{user?.username || 'Usuário'}</strong>! Gerencie seus bots de vendas
            </p>
          </div>
          <div className="flex items-center gap-3">
            {/* Botão principal - Criar Novo Bot (só aparece se não tiver bot E tiver plano válido) */}
            {bots.length === 0 && license?.plan && license.plan !== 'free' && license?.status === 'active' && (
              <Button
                onClick={() => {
                  const configUrl = userFullData?.urlToken
                    ? `/configure?username=${user?.username}&userId=${user?.id}&urlToken=${userFullData.urlToken}`
                    : `/configure?username=${user?.username}&userId=${user?.id}`;
                  navigate(configUrl);
                }}
                className="bg-[#5865F2] hover:bg-[#4752C4] text-white font-semibold px-6 py-2 rounded-xl transition-all duration-300 transform hover:scale-105 shadow-lg"
              >
                <Plus className="mr-2 h-4 w-4" />
                Criar Novo Bot
              </Button>
            )}

            {/* Mensagem quando não tem plano válido */}
            {bots.length === 0 && (!license?.plan || license.plan === 'free' || license?.status !== 'active') && (
              <div className="flex items-center gap-2 px-4 py-2 bg-orange-500/10 border border-orange-500/20 rounded-xl">
                <AlertTriangle className="w-4 h-4 text-orange-400" />
                <span className="text-orange-400 text-sm font-medium">
                  Plano necessário para criar bots
                </span>
              </div>
            )}

            {/* Mensagem quando já tem bot */}
            {bots.length > 0 && (
              <div className="flex items-center gap-2 px-4 py-2 bg-blue-500/10 border border-blue-500/20 rounded-xl">
                <Bot className="w-4 h-4 text-blue-400" />
                <span className="text-blue-400 text-sm font-medium">
                  Limite de 1 bot atingido
                </span>
              </div>
            )}

            {/* Botão Ver Plano Atual */}
            <Button
              variant="outline"
              onClick={() => {
                setShowLicenseDetails(!showLicenseDetails);
                // Recarregar dados da licença se necessário
                if (!license) {
                  loadUserLicense();
                }
                // Scroll para a seção após um pequeno delay para permitir a renderização
                setTimeout(() => {
                  const licenseSection = document.querySelector('[data-license-section]');
                  if (licenseSection) {
                    licenseSection.scrollIntoView({ behavior: 'smooth' });
                  }
                }, 100);
              }}
              className="backdrop-blur-sm bg-purple-500/10 border-purple-500/20 text-purple-400 hover:bg-purple-500/20 transition-all duration-300"
            >
              <Bot className="mr-2 h-4 w-4" />
              {showLicenseDetails ? 'Ocultar Plano' : 'Ver Plano Atual'}
            </Button>

            {/* Botão secundário - Voltar ao Início */}
            <Button
              variant="outline"
              onClick={() => {
                navigate('/');
              }}
              className="backdrop-blur-sm bg-white/10 border-white/20 text-white hover:bg-white/20 transition-all duration-300"
            >
              <Home className="mr-2 h-4 w-4" />
              Início
            </Button>

            {/* Botão de logout */}
            <Button
              variant="outline"
              size="icon"
              onClick={() => {
                // Limpar todos os tokens e redirecionar
                setUser(null);
                setUserFullData(null);
                setBots([]);
                logout();
              }}
              className="backdrop-blur-sm bg-red-500/10 border-red-500/20 text-red-400 hover:bg-red-500/20 transition-all duration-300"
              title="Fazer Logout"
            >
              <LogOut className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {/* Resumo do Plano (sempre visível) */}
        {license && (
          <div className="mb-6">
            <Card className="backdrop-blur-sm bg-white/5 border-white/10 shadow-lg">
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-4">
                    <div className="flex items-center gap-2">
                      <Bot className="w-5 h-5 text-primary" />
                      <span className="text-white font-medium">Plano Atual:</span>
                    </div>
                    <span className={`px-3 py-1 rounded-lg text-sm font-semibold ${
                      license?.plan === 'pro' ? 'bg-gradient-to-r from-purple-600 to-purple-700 text-white' :
                      license?.plan === 'basic' ? 'bg-gradient-to-r from-blue-500 to-blue-600 text-white' :
                      'bg-gray-500/20 text-gray-400'
                    }`}>
                      {license?.plan === 'pro' ? 'PRO' :
                       license?.plan === 'basic' ? 'BASIC' : 'FREE'}
                    </span>
                    <div className="flex items-center gap-2">
                      <span className={`w-2 h-2 rounded-full ${
                        license?.status === 'active' ? 'bg-green-500 animate-pulse' : 'bg-gray-500'
                      }`}></span>
                      <span className="text-sm text-muted-foreground">
                        {license?.status === 'active' ? 'Ativo' : 'Inativo'}
                      </span>
                    </div>
                  </div>
                  {license.hasLicense && (
                    <div className="text-right">
                      <p className="text-sm text-muted-foreground">Tempo Restante</p>
                      <p className="text-white font-medium">
                        {(() => {
                          // Debug: verificar o que está chegando do backend
                          console.log('DEBUG Frontend - license object:', license);
                          console.log('DEBUG Frontend - daysRemaining:', license.daysRemaining);

                          // Usar o valor calculado pelo backend
                          if (license.daysRemaining !== undefined) {
                            if (license.daysRemaining > 0) {
                              return `${license.daysRemaining} dias`;
                            } else {
                              return 'Expirado';
                            }
                          }
                          return 'Indefinido';
                        })()}
                      </p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Informações Detalhadas da Licença */}
        {(license && showLicenseDetails) && (
          <div className="mb-8" data-license-section>
            <Card className="backdrop-blur-sm bg-white/5 border-white/10 shadow-lg">
              <CardHeader>
                <CardTitle className="flex items-center justify-between text-white">
                  <div className="flex items-center gap-2">
                    <Bot className="w-5 h-5 text-primary" />
                    Informações da Licença
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={loadUserLicense}
                    className="backdrop-blur-sm bg-white/10 border-white/20 text-white hover:bg-white/20 transition-all duration-300"
                  >
                    <RotateCcw className="w-4 h-4" />
                  </Button>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Informações do Plano */}
                  <div className="space-y-4">
                    <div>
                      <p className="text-sm text-muted-foreground mb-2">Plano Atual</p>
                      <div className="flex items-center gap-3">
                        <span className={`px-4 py-2 rounded-xl text-sm font-semibold ${
                          license?.plan === 'pro' ? 'bg-gradient-to-r from-purple-600 to-purple-700 text-white shadow-lg' :
                          license?.plan === 'basic' ? 'bg-gradient-to-r from-blue-500 to-blue-600 text-white' :
                          'bg-gray-500/20 text-gray-400'
                        }`}>
                          {license?.plan === 'pro' ? 'PRO' :
                           license?.plan === 'basic' ? 'BASIC' : 'FREE'}
                        </span>
                        <div className="flex items-center gap-2">
                          <span className={`w-2 h-2 rounded-full ${
                            license?.status === 'active' ? 'bg-green-500 animate-pulse' : 'bg-gray-500'
                          }`}></span>
                          <span className="text-sm text-muted-foreground">
                            {license?.status === 'active' ? 'Ativo' : 'Inativo'}
                          </span>
                        </div>
                      </div>
                    </div>

                    {license.hasLicense && (
                      <div>
                        <p className="text-sm text-muted-foreground mb-2">Tempo Restante</p>
                        <p className="text-white font-medium">
                          {(() => {
                            // Usar o valor calculado pelo backend
                            if (license.daysRemaining !== undefined) {
                              if (license.daysRemaining > 0) {
                                return `${license.daysRemaining} dias restantes`;
                              } else {
                                return 'Plano expirado';
                              }
                            }
                            return 'Renovação automática';
                          })()}
                        </p>
                        {license.createdAt && (
                          <p className="text-xs text-muted-foreground mt-1">
                            Ativo desde {new Date(license.createdAt).toLocaleDateString('pt-BR')}
                          </p>
                        )}
                      </div>
                    )}
                  </div>

                  {/* Recursos do Plano */}
                  <div className="space-y-4">
                    <div>
                      <p className="text-sm text-muted-foreground mb-3">Recursos Inclusos</p>
                      <div className="space-y-2">
                        {license?.plan === 'pro' ? (
                          <>
                            <div className="flex items-center gap-2 text-sm">
                              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                              <span className="text-white">Vendas e produtos ilimitados</span>
                            </div>
                            <div className="flex items-center gap-2 text-sm">
                              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                              <span className="text-white">Suporte prioritário 24/7</span>
                            </div>
                            <div className="flex items-center gap-2 text-sm">
                              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                              <span className="text-white">Analytics avançado</span>
                            </div>
                            <div className="flex items-center gap-2 text-sm">
                              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                              <span className="text-white">Backup e autenticação avançada</span>
                            </div>
                          </>
                        ) : license?.plan === 'basic' ? (
                          <>
                            <div className="flex items-center gap-2 text-sm">
                              <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                              <span className="text-white">Vendas e produtos ilimitados</span>
                            </div>
                            <div className="flex items-center gap-2 text-sm">
                              <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                              <span className="text-white">Sistema de tickets</span>
                            </div>
                            <div className="flex items-center gap-2 text-sm">
                              <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                              <span className="text-white">Proteção anti-fraude</span>
                            </div>
                            <div className="flex items-center gap-2 text-sm">
                              <div className="w-2 h-2 bg-gray-500 rounded-full"></div>
                              <span className="text-gray-400">Suporte prioritário</span>
                            </div>
                          </>
                        ) : (
                          <div className="text-sm text-gray-400">
                            Recursos limitados do plano gratuito
                          </div>
                        )}
                      </div>
                    </div>

                    {/* Botão de Upgrade */}
                    <div className="pt-2">
                      {license?.plan !== 'pro' && (
                        <Button
                          onClick={async () => {
                            try {
                              // Verificar se está logado
                              const sessionToken = localStorage.getItem('sessionToken');

                              if (!sessionToken) {
                                toast.error('Você precisa estar logado para fazer upgrade');
                                return;
                              }

                              // Testar se o token é válido fazendo uma requisição simples
                              const testResponse = await fetch('/api/user-license', {
                                headers: {
                                  'x-session-token': sessionToken,
                                },
                              });

                              if (testResponse.status === 401) {
                                // Token inválido - redirecionar para login
                                toast.error('Sessão expirada. Redirecionando para login...');
                                setTimeout(() => {
                                  window.location.href = `/auth/discord?redirect=${encodeURIComponent('/plan-callback?plan=pro')}`;
                                }, 1500);
                                return;
                              }

                              // Ir direto para checkout do Pro
                              const priceId = 'price_1RdZ0ORIzvHRKrKIt3O227mH'; // Pro plan

                              const response = await fetch('/api/stripe/create-checkout-session', {
                                method: 'POST',
                                headers: {
                                  'Content-Type': 'application/json',
                                  'x-session-token': sessionToken,
                                },
                                body: JSON.stringify({
                                  priceId: priceId,
                                  plan: 'pro',
                                  successUrl: `${window.location.origin}/success`,
                                  cancelUrl: `${window.location.origin}/dashboard`,
                                }),
                              });

                              if (!response.ok) {
                                if (response.status === 401) {
                                  // Token inválido - redirecionar para login
                                  toast.error('Sessão expirada. Redirecionando para login...');
                                  setTimeout(() => {
                                    window.location.href = `/auth/discord?redirect=${encodeURIComponent('/plan-callback?plan=pro')}`;
                                  }, 1500);
                                  return;
                                }
                                throw new Error('Erro ao criar sessão de checkout');
                              }

                              const { sessionId } = await response.json();

                              // Redirecionar para Stripe Checkout
                              const stripe = await import('@stripe/stripe-js').then(m =>
                                m.loadStripe('pk_test_51RdYpaRIzvHRKrKI5lpipw0j0NqSDi7VP4yn30XKkFt9yOICAeEeWCuHhwm3d8qb5mWmE0J4fn6pBOPAzNNOmu4U00lLhT8skQ')
                              );

                              if (stripe) {
                                await stripe.redirectToCheckout({ sessionId });
                              }
                            } catch (error) {
                              console.error('Erro ao processar upgrade:', error);
                              toast.error('Erro ao processar upgrade');
                            }
                          }}
                          className="bg-[#5865F2] hover:bg-[#4752C4] text-white font-semibold px-4 py-2 rounded-xl transition-all duration-300 transform hover:scale-105 shadow-lg"
                          size="sm"
                        >
                          <TrendingUp className="mr-2 h-4 w-4" />
                          {license?.plan === 'basic' ? 'Upgrade para PRO' : 'Escolher Plano'}
                        </Button>
                      )}
                    </div>
                  </div>
                </div>

                {!license.hasLicense && (
                  <div className="mt-6 p-4 bg-gradient-to-r from-yellow-500/10 to-orange-500/10 border border-yellow-500/20 rounded-xl">
                    <div className="flex items-start gap-3">
                      <AlertTriangle className="w-5 h-5 text-yellow-500 mt-0.5" />
                      <div>
                        <p className="text-yellow-400 font-medium text-sm mb-1">
                          Plano Gratuito Ativo
                        </p>
                        <p className="text-yellow-600 dark:text-yellow-400 text-sm">
                          Desbloqueie todo o potencial com recursos avançados e suporte prioritário.
                        </p>
                      </div>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        )}

        {/* Alerta de Token Inválido */}
        {tokenStatus && !tokenStatus.tokenValid && tokenStatus.hasBot && (
          <div className="mb-8">
            <Card className="backdrop-blur-sm bg-red-500/10 border-red-500/20 shadow-lg">
              <CardContent className="p-6">
                <div className="flex items-start gap-4">
                  <div className="w-12 h-12 bg-red-500/20 rounded-xl border border-red-500/30 flex items-center justify-center flex-shrink-0">
                    <AlertTriangle className="w-6 h-6 text-red-500" />
                  </div>
                  <div className="flex-1">
                    <h3 className="text-lg font-semibold text-red-400 mb-2">
                      Token do Bot Inválido
                    </h3>
                    <p className="text-red-300 text-sm mb-4">
                      O token do seu bot Discord foi invalidado ou regenerado. Isso pode acontecer quando você regenera o token no Discord Developer Portal.
                      Você precisa atualizar o token para que o bot funcione novamente.
                    </p>
                    <div className="flex gap-3">
                      <Button
                        onClick={() => {
                          const configUrl = userFullData?.urlToken
                            ? `/configure?username=${user?.username}&userId=${user?.id}&urlToken=${userFullData.urlToken}`
                            : `/configure?username=${user?.username}&userId=${user?.id}`;
                          navigate(configUrl);
                        }}
                        className="bg-red-500 hover:bg-red-600 text-white font-semibold px-4 py-2 rounded-xl transition-all duration-300"
                        size="sm"
                      >
                        <Settings className="mr-2 h-4 w-4" />
                        Atualizar Token
                      </Button>
                      <Button
                        onClick={clearTokenInvalid}
                        variant="outline"
                        className="backdrop-blur-sm bg-white/10 border-white/20 text-white hover:bg-white/20 transition-all duration-300"
                        size="sm"
                      >
                        <X className="mr-2 h-4 w-4" />
                        Dispensar Aviso
                      </Button>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Estatísticas gerais */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card className="backdrop-blur-sm bg-white/5 border-white/10 shadow-lg hover:bg-white/10 transition-all duration-300">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-muted-foreground">Total de Bots</p>
                  <p className="text-2xl font-bold text-white">{bots.length}</p>
                </div>
                <div className="w-12 h-12 bg-primary/20 rounded-xl border border-primary/30 flex items-center justify-center">
                  <Bot className="w-6 h-6 text-primary" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="backdrop-blur-sm bg-white/5 border-white/10 shadow-lg hover:bg-white/10 transition-all duration-300">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-muted-foreground">Bots Online</p>
                  <p className="text-2xl font-bold text-green-400">
                    {bots.filter(bot => bot.status === 'online').length}
                  </p>
                </div>
                <div className="w-12 h-12 bg-green-500/20 rounded-xl border border-green-500/30 flex items-center justify-center">
                  <Play className="w-6 h-6 text-green-500" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="backdrop-blur-sm bg-white/5 border-white/10 shadow-lg hover:bg-white/10 transition-all duration-300">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-muted-foreground">Total Servidores</p>
                  <p className="text-2xl font-bold text-blue-400">
                    {bots.reduce((total, bot) => total + bot.servers, 0)}
                  </p>
                </div>
                <div className="w-12 h-12 bg-blue-500/20 rounded-xl border border-blue-500/30 flex items-center justify-center">
                  <Users className="w-6 h-6 text-blue-500" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="backdrop-blur-sm bg-white/5 border-white/10 shadow-lg hover:bg-white/10 transition-all duration-300">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-muted-foreground">Total Vendas</p>
                  <p className="text-2xl font-bold text-yellow-400">
                    {bots.reduce((total, bot) => total + bot.sales, 0)}
                  </p>
                </div>
                <div className="w-12 h-12 bg-yellow-500/20 rounded-xl border border-yellow-500/30 flex items-center justify-center">
                  <ShoppingCart className="w-6 h-6 text-yellow-500" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Lista de Bots */}
        <Card className="backdrop-blur-sm bg-white/5 border-white/10 shadow-lg">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-white">
              <Bot className="w-5 h-5 text-primary" />
              Aplicações de {user?.username || 'usuário'}
            </CardTitle>
            <CardDescription>
              Gerencie todos os seus bots de vendas em um só lugar
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {bots.length === 0 ? (
              <div className="text-center py-12">
                <Bot className="w-16 h-16 text-muted-foreground mx-auto mb-4" />

                {/* Verificar se tem plano válido */}
                {license?.plan && license.plan !== 'free' && license?.status === 'active' ? (
                  <>
                    <h3 className="text-lg font-medium text-foreground mb-2">Nenhuma aplicação configurada</h3>
                    <p className="text-muted-foreground mb-4">
                      Crie sua primeira aplicação de vendas para começar
                    </p>
                    <Button
                      onClick={() => {
                        const configUrl = userFullData?.urlToken
                          ? `/configure?username=${user?.username}&userId=${user?.id}&urlToken=${userFullData.urlToken}`
                          : `/configure?username=${user?.username}&userId=${user?.id}`;
                        navigate(configUrl);
                      }}
                      className="bg-green-600 hover:bg-green-700 text-white"
                    >
                      <Plus className="mr-2 h-4 w-4" />
                      Cadastrar uma nova aplicação
                    </Button>
                  </>
                ) : (
                  <>
                    <h3 className="text-lg font-medium text-foreground mb-2">Plano necessário</h3>
                    <p className="text-muted-foreground mb-4">
                      Você não tem nenhum plano ativo para usar nosso serviço.<br />
                      Adquira um plano para começar a criar seus bots de vendas.
                    </p>
                    <Button
                      onClick={async () => {
                        try {
                          // Verificar se está logado
                          const sessionToken = localStorage.getItem('sessionToken');

                          if (!sessionToken) {
                            toast.error('Você precisa estar logado para adquirir um plano');
                            return;
                          }

                          // Testar se o token é válido fazendo uma requisição simples
                          const testResponse = await fetch('/api/user-license', {
                            headers: {
                              'x-session-token': sessionToken,
                            },
                          });

                          if (testResponse.status === 401) {
                            // Token inválido - redirecionar para login
                            toast.error('Sessão expirada. Redirecionando para login...');
                            setTimeout(() => {
                              window.location.href = `/auth/discord?redirect=${encodeURIComponent('/plan-callback?plan=pro')}`;
                            }, 1500);
                            return;
                          }

                          // Ir direto para checkout do Pro
                          const priceId = 'price_1RdZ0ORIzvHRKrKIt3O227mH'; // Pro plan

                          const response = await fetch('/api/stripe/create-checkout-session', {
                            method: 'POST',
                            headers: {
                              'Content-Type': 'application/json',
                              'x-session-token': sessionToken,
                            },
                            body: JSON.stringify({
                              priceId: priceId,
                              plan: 'pro',
                              successUrl: `${window.location.origin}/success`,
                              cancelUrl: `${window.location.origin}/dashboard`,
                            }),
                          });

                          if (!response.ok) {
                            if (response.status === 401) {
                              // Token inválido - redirecionar para login
                              toast.error('Sessão expirada. Redirecionando para login...');
                              setTimeout(() => {
                                window.location.href = `/auth/discord?redirect=${encodeURIComponent('/plan-callback?plan=pro')}`;
                              }, 1500);
                              return;
                            }
                            throw new Error('Erro ao criar sessão de checkout');
                          }

                          const { sessionId } = await response.json();

                          // Redirecionar para Stripe Checkout
                          const stripe = await import('@stripe/stripe-js').then(m =>
                            m.loadStripe('pk_test_51RdYpaRIzvHRKrKI5lpipw0j0NqSDi7VP4yn30XKkFt9yOICAeEeWCuHhwm3d8qb5mWmE0J4fn6pBOPAzNNOmu4U00lLhT8skQ')
                          );

                          if (stripe) {
                            await stripe.redirectToCheckout({ sessionId });
                          }
                        } catch (error) {
                          console.error('Erro ao processar compra:', error);
                          toast.error('Erro ao processar compra');
                        }
                      }}
                      className="bg-[#5865F2] hover:bg-[#4752C4] text-white font-semibold px-6 py-2 rounded-xl transition-all duration-300 transform hover:scale-105 shadow-lg"
                    >
                      <TrendingUp className="mr-2 h-4 w-4" />
                      Adquirir Plano PRO
                    </Button>
                  </>
                )}
              </div>
            ) : (
              bots.map((bot) => (
                <div key={bot.id} className="bg-gray-800 border border-gray-700 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center gap-3">
                      <Avatar className="w-12 h-12">
                        <AvatarImage
                          src={bot.avatar}
                          alt={`Avatar do ${bot.name}`}
                          className="object-cover"
                        />
                        <AvatarFallback className="bg-gray-700 text-gray-400">
                          <Bot className="w-6 h-6" />
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <h4 className="font-medium text-white">{bot.name}</h4>
                        <div className="flex items-center gap-2">
                          <div className={`w-2 h-2 rounded-full ${getStatusDot(bot.status)}`}></div>
                          <span className={`text-sm ${getStatusColor(bot.status)}`}>
                            {getStatusText(bot.status)}
                          </span>
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      {/* Botão Ligar/Desligar */}
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button
                            size="sm"
                            onClick={() => handleBotAction(bot.id, bot.status === 'online' ? 'stop' : 'start')}
                            className={bot.status === 'online'
                              ? "bg-red-600 hover:bg-red-700 text-white transition-all duration-200"
                              : "bg-green-600 hover:bg-green-700 text-white transition-all duration-200"
                            }
                          >
                            {bot.status === 'online' ? (
                              <Pause className="w-4 h-4" />
                            ) : (
                              <Play className="w-4 h-4" />
                            )}
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent>
                          {bot.status === 'online' ? 'Parar bot' : 'Iniciar bot'}
                        </TooltipContent>
                      </Tooltip>

                      {/* Botão Reiniciar */}
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button
                            size="sm"
                            onClick={() => handleBotAction(bot.id, 'restart')}
                            className="bg-blue-600 hover:bg-blue-700 text-white transition-all duration-200"
                          >
                            <RotateCcw className={`w-4 h-4 ${restartingBots.has(bot.id) ? 'animate-spin-slow' : ''}`} />
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent>
                          Reiniciar bot
                        </TooltipContent>
                      </Tooltip>

                      {/* Botão Convidar */}
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button
                            size="sm"
                            onClick={() => window.open(`https://discord.com/api/oauth2/authorize?client_id=${bot.clientId}&permissions=2048&scope=bot`, '_blank')}
                            className="bg-gray-600 hover:bg-gray-700 text-white transition-all duration-200"
                          >
                            <ExternalLink className="w-4 h-4" />
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent>
                          Convidar para servidor
                        </TooltipContent>
                      </Tooltip>

                      {/* Botão Apagar */}
                      <AlertDialog>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <AlertDialogTrigger asChild>
                              <Button
                                size="sm"
                                className="bg-red-600 hover:bg-red-700 text-white transition-all duration-200"
                              >
                                <Trash2 className="w-4 h-4" />
                              </Button>
                            </AlertDialogTrigger>
                          </TooltipTrigger>
                          <TooltipContent>
                            Remover aplicação
                          </TooltipContent>
                        </Tooltip>
                        <AlertDialogContent className="bg-gray-900 border-gray-700">
                          <AlertDialogHeader>
                            <AlertDialogTitle className="flex items-center gap-2 text-red-400">
                              <AlertTriangle className="w-5 h-5" />
                              Confirmar Exclusão da Aplicação
                            </AlertDialogTitle>
                            <AlertDialogDescription className="text-gray-300 space-y-2">
                              <p>
                                Você está prestes a <strong className="text-red-400">excluir permanentemente</strong> a aplicação <strong className="text-white">"{bot.name}"</strong>.
                              </p>
                              <p className="text-sm text-gray-400">
                                Esta ação irá:
                              </p>
                              <ul className="text-sm text-gray-400 list-disc list-inside space-y-1 ml-4">
                                <li>Desconectar o bot de todos os servidores</li>
                                <li>Remover todas as configurações e dados</li>
                                <li>Cancelar todas as vendas em andamento</li>
                                <li>Apagar o histórico de transações</li>
                              </ul>
                              <p className="text-sm font-medium text-yellow-400 mt-3">
                                ⚠️ Esta ação não pode ser desfeita.
                              </p>
                            </AlertDialogDescription>
                          </AlertDialogHeader>
                          <AlertDialogFooter className="gap-2">
                            <AlertDialogCancel className="bg-gray-700 hover:bg-gray-600 text-white border-gray-600">
                              Cancelar
                            </AlertDialogCancel>
                            <AlertDialogAction
                              onClick={() => handleBotAction(bot.id, 'delete')}
                              className="bg-red-600 hover:bg-red-700 text-white"
                            >
                              <Trash2 className="w-4 h-4 mr-2" />
                              Excluir Aplicação
                            </AlertDialogAction>
                          </AlertDialogFooter>
                        </AlertDialogContent>
                      </AlertDialog>
                    </div>
                  </div>

                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm text-gray-300">
                    <div>
                      <span className="text-gray-400">ID:</span> {bot.clientId.slice(0, 8)}...
                    </div>
                    <div>
                      <span className="text-gray-400">Servidores:</span> <span className="text-blue-400">{bot.servers}</span>
                    </div>
                    <div>
                      <span className="text-gray-400">Vendas:</span> <span className="text-yellow-400">{bot.sales}</span>
                    </div>
                    <div>
                      <span className="text-gray-400">Última venda:</span> {bot.lastSale}
                    </div>
                  </div>
                </div>
              ))
            )}
          </CardContent>
        </Card>
        </div>
      </div>
    </TooltipProvider>
  );
};

export default Dashboard;
