import React, { useState, useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Bot, ExternalLink, Settings, CheckCircle, Clock, AlertCircle } from 'lucide-react';
import { authenticatedFetch } from '@/lib/auth';

interface SaaSBot {
  id: string;
  name: string;
  plan: 'basic' | 'pro';
  avatar: string;
  invite_url: string;
  description: string;
  features: string[];
  status: 'online' | 'offline';
}

interface UserConfiguration {
  id: string;
  discord_id: string;
  discord_username: string;
  license_plan: 'basic' | 'pro';
  license_validated: boolean;
  guild_id?: string;
  guild_name?: string;
}

interface LicenseData {
  hasLicense: boolean;
  plan: 'basic' | 'pro';
  status: 'active' | 'expired' | 'inactive';
  licenseKey: string;
  daysRemaining: number;
  isExpired: boolean;
}

export default function SaaSDashboard() {
  const [availableBots, setAvailableBots] = useState<SaaSBot[]>([]);
  const [userConfig, setUserConfig] = useState<UserConfiguration | null>(null);
  const [license, setLicense] = useState<LicenseData | null>(null);
  const [user, setUser] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();

  // Carregar dados do usuário
  const loadUserInfo = async () => {
    try {
      const response = await authenticatedFetch('/api/user-info');
      if (response.ok) {
        const userData = await response.json();
        setUser(userData);
        await loadUserConfiguration(userData.id);
      }
    } catch (error) {
      console.error('Erro ao carregar informações do usuário:', error);
    }
  };

  // Carregar configuração do usuário
  const loadUserConfiguration = async (discordId: string) => {
    try {
      const response = await authenticatedFetch(`/api/user-config/${discordId}`);
      if (response.ok) {
        const config = await response.json();
        setUserConfig(config);
      } else if (response.status === 404) {
        await createUserConfiguration(discordId);
      }
    } catch (error) {
      console.error('Erro ao carregar configuração do usuário:', error);
    }
  };

  // Criar configuração inicial do usuário
  const createUserConfiguration = async (discordId: string) => {
    try {
      const response = await authenticatedFetch('/api/user-config', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          discord_id: discordId,
          discord_username: user?.username || '',
          license_plan: license?.plan || 'basic',
          license_validated: license?.status === 'active' || false,
        }),
      });

      if (response.ok) {
        const config = await response.json();
        setUserConfig(config);
      }
    } catch (error) {
      console.error('Erro ao criar configuração do usuário:', error);
    }
  };

  // Carregar licença do usuário
  const loadLicense = async () => {
    try {
      const response = await authenticatedFetch('/api/user-license');
      if (response.ok) {
        const licenseData = await response.json();
        setLicense(licenseData);
      }
    } catch (error) {
      console.error('Erro ao carregar licença:', error);
    }
  };

  // Carregar bots disponíveis
  const loadAvailableBots = async () => {
    try {
      const response = await authenticatedFetch('/api/saas-bots');
      if (response.ok) {
        const bots = await response.json();
        setAvailableBots(bots);
      } else {
        // Fallback para bots estáticos
        const fallbackBots: SaaSBot[] = [
          {
            id: 'nodex-basic',
            name: 'Nodex Bot Basic',
            plan: 'basic',
            avatar: '/placeholder.svg',
            invite_url: `https://discord.com/api/oauth2/authorize?client_id=1392176939237900308&permissions=8&scope=bot%20applications.commands`,
            description: 'Bot básico com funcionalidades essenciais para vendas',
            features: [
              'Sistema de vendas básico',
              'Até 50 produtos',
              'Pagamentos via PIX Manual',
              'Logs básicos',
              'Sistema de tickets básico'
            ],
            status: 'online'
          },
          {
            id: 'nodex-pro',
            name: 'Nodex Bot Pro',
            plan: 'pro',
            avatar: '/placeholder.svg',
            invite_url: `https://discord.com/api/oauth2/authorize?client_id=1392177391237070859&permissions=8&scope=bot%20applications.commands`,
            description: 'Bot profissional com recursos avançados',
            features: [
              'Sistema de vendas avançado',
              'Produtos ilimitados',
              'Mercado Pago + PIX Manual',
              'Sistema de tickets completo',
              'Anti-fake avançado',
              'Logs completos',
              'Relatórios detalhados'
            ],
            status: 'online'
          }
        ];
        setAvailableBots(fallbackBots);
      }
    } catch (error) {
      console.error('Erro ao carregar bots disponíveis:', error);
    }
  };

  // Inicializar dados
  useEffect(() => {
    const initializeData = async () => {
      setIsLoading(true);
      await Promise.all([
        loadUserInfo(),
        loadLicense(),
        loadAvailableBots()
      ]);
      setIsLoading(false);
    };

    initializeData();
  }, []);

  if (isLoading) {
    return (
      <div className="min-h-screen bg-[#0A0A0A] flex items-center justify-center">
        <div className="text-white">Carregando...</div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-[#0A0A0A] text-white">
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold mb-2">Dashboard - Sistema SaaS</h1>
          <p className="text-gray-400">Gerencie seus bots centralizados</p>
        </div>

        {/* Informações da licença */}
        {license && (
          <Card className="bg-gray-900 border-gray-700 mb-6">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                {license.status === 'active' ? (
                  <CheckCircle className="w-5 h-5 text-green-500" />
                ) : license.status === 'expired' ? (
                  <AlertCircle className="w-5 h-5 text-red-500" />
                ) : (
                  <Clock className="w-5 h-5 text-yellow-500" />
                )}
                Licença {license.plan.toUpperCase()}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <p className="text-gray-400">Status</p>
                  <Badge variant={license.status === 'active' ? 'default' : 'destructive'}>
                    {license.status === 'active' ? 'Ativa' : license.status === 'expired' ? 'Expirada' : 'Inativa'}
                  </Badge>
                </div>
                <div>
                  <p className="text-gray-400">Dias restantes</p>
                  <p className="font-semibold">{license.daysRemaining} dias</p>
                </div>
                <div>
                  <p className="text-gray-400">Chave da licença</p>
                  <p className="font-mono text-sm">{license.licenseKey}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Bots disponíveis */}
        <Card className="bg-gray-900 border-gray-700">
          <CardHeader>
            <CardTitle>Bots Disponíveis</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {license?.status === 'active' ? (
              availableBots
                .filter(bot => bot.plan === license.plan || (license.plan === 'pro' && bot.plan === 'basic'))
                .map((bot) => (
                  <div key={bot.id} className="bg-gray-800 border border-gray-700 rounded-lg p-6">
                    <div className="flex items-center justify-between mb-4">
                      <div className="flex items-center gap-4">
                        <Avatar className="w-16 h-16">
                          <AvatarImage src={bot.avatar} alt={`Avatar do ${bot.name}`} />
                          <AvatarFallback className="bg-gray-700 text-gray-400">
                            <Bot className="w-8 h-8" />
                          </AvatarFallback>
                        </Avatar>
                        <div>
                          <h4 className="font-semibold text-white text-lg">{bot.name}</h4>
                          <p className="text-gray-400 text-sm mb-2">{bot.description}</p>
                          <div className="flex items-center gap-2">
                            <div className={`w-2 h-2 rounded-full ${bot.status === 'online' ? 'bg-green-500' : 'bg-red-500'}`}></div>
                            <span className={`text-sm ${bot.status === 'online' ? 'text-green-400' : 'text-red-400'}`}>
                              {bot.status === 'online' ? 'Online' : 'Offline'}
                            </span>
                            <Badge variant={bot.plan === 'pro' ? 'default' : 'secondary'}>
                              {bot.plan.toUpperCase()}
                            </Badge>
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <Button
                          onClick={() => window.open(bot.invite_url, '_blank')}
                          className="bg-[#5865F2] hover:bg-[#4752C4] text-white"
                        >
                          <ExternalLink className="mr-2 h-4 w-4" />
                          Convidar Bot
                        </Button>
                        {userConfig?.guild_id && (
                          <Button
                            onClick={() => navigate(`/configure/${bot.id}`)}
                            variant="outline"
                            className="border-gray-600 text-gray-300 hover:bg-gray-700"
                          >
                            <Settings className="mr-2 h-4 w-4" />
                            Configurar
                          </Button>
                        )}
                      </div>
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                      <div>
                        <h5 className="font-medium text-white mb-2">Recursos Inclusos:</h5>
                        <ul className="space-y-1">
                          {bot.features.map((feature, index) => (
                            <li key={index} className="text-sm text-gray-400 flex items-center gap-2">
                              <div className="w-1.5 h-1.5 bg-green-500 rounded-full"></div>
                              {feature}
                            </li>
                          ))}
                        </ul>
                      </div>
                      {userConfig?.guild_id && (
                        <div>
                          <h5 className="font-medium text-white mb-2">Servidor Configurado:</h5>
                          <p className="text-sm text-gray-400">{userConfig.guild_name || 'Servidor não identificado'}</p>
                        </div>
                      )}
                    </div>
                  </div>
                ))
            ) : (
              <div className="text-center py-12">
                <Bot className="w-16 h-16 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-medium text-foreground mb-2">Licença necessária</h3>
                <p className="text-muted-foreground mb-4">
                  Você precisa de uma licença ativa para usar os bots SaaS
                </p>
                <Button
                  onClick={() => navigate('/pricing')}
                  className="bg-[#5865F2] hover:bg-[#4752C4] text-white"
                >
                  Adquirir Licença
                </Button>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
