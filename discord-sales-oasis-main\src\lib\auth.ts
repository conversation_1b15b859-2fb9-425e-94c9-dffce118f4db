// Função para fazer requisições autenticadas
export const authenticatedFetch = async (url: string, options: RequestInit = {}) => {
  const token = localStorage.getItem('discord_token');
  
  const headers = {
    'Content-Type': 'application/json',
    ...(token && { 'Authorization': `Bear<PERSON> ${token}` }),
    ...options.headers,
  };

  return fetch(url, {
    ...options,
    headers,
  });
};

// Função para verificar se o usuário está autenticado
export const isAuthenticated = (): boolean => {
  const token = localStorage.getItem('discord_token');
  return !!token;
};

// Função para fazer logout
export const logout = () => {
  localStorage.removeItem('discord_token');
  localStorage.removeItem('discord_user');
  window.location.href = '/';
};

// Função para obter dados do usuário do localStorage
export const getUser = () => {
  const userStr = localStorage.getItem('discord_user');
  return userStr ? JSON.parse(userStr) : null;
};
