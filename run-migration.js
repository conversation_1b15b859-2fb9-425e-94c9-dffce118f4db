const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_SERVICE_ROLE_KEY);

async function runMigration() {
  try {
    console.log('🔄 Executando migração do banco de dados...');
    
    // Criar tabela user_configurations
    const { error: configError } = await supabase
      .from('user_configurations')
      .select('id')
      .limit(1);
    
    if (configError && configError.code === '42P01') {
      // Tabela não existe, criar
      console.log('📝 Criando tabela user_configurations...');
      
      const { error: createError } = await supabase.rpc('exec_sql', {
        sql: `
          CREATE TABLE user_configurations (
            id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
            discord_id VARCHAR(20) UNIQUE NOT NULL,
            discord_username VARCHAR(100) NOT NULL,
            discord_discriminator VARCHAR(10),
            discord_avatar VARCHAR(100),
            email VARCHAR(255),
            license_key VARCHAR(30),
            license_plan VARCHAR(20) NOT NULL DEFAULT 'basic',
            license_activated_at TIMESTAMP WITH TIME ZONE,
            license_expires_at TIMESTAMP WITH TIME ZONE,
            license_validated BOOLEAN DEFAULT false,
            guild_id VARCHAR(20),
            guild_name VARCHAR(100),
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            last_activity TIMESTAMP WITH TIME ZONE DEFAULT NOW()
          );
        `
      });
      
      if (createError) {
        console.error('Erro ao criar tabela user_configurations:', createError);
      } else {
        console.log('✅ Tabela user_configurations criada com sucesso!');
      }
    } else {
      console.log('✅ Tabela user_configurations já existe!');
    }
    
    // Criar outras tabelas necessárias
    await createOtherTables();
    
    console.log('🎉 Migração concluída!');
  } catch (error) {
    console.error('❌ Erro na migração:', error);
  }
}

async function createOtherTables() {
  const tables = [
    {
      name: 'user_categories',
      sql: `
        CREATE TABLE IF NOT EXISTS user_categories (
          id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
          user_id UUID REFERENCES user_configurations(id) ON DELETE CASCADE,
          category_id VARCHAR(50) NOT NULL,
          name VARCHAR(100) NOT NULL,
          discord_category_id VARCHAR(20),
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          UNIQUE(user_id, category_id)
        );
      `
    },
    {
      name: 'user_products',
      sql: `
        CREATE TABLE IF NOT EXISTS user_products (
          id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
          user_id UUID REFERENCES user_configurations(id) ON DELETE CASCADE,
          product_id VARCHAR(50) NOT NULL,
          category_id VARCHAR(50) NOT NULL,
          name VARCHAR(200) NOT NULL,
          description TEXT,
          price DECIMAL(10,2) DEFAULT 0,
          stock INTEGER DEFAULT 0,
          image_url TEXT,
          banner_url TEXT,
          thumbnail_url TEXT,
          color VARCHAR(7) DEFAULT '#0099ff',
          channel_id VARCHAR(20),
          embed_message_id VARCHAR(20),
          buy_button_text VARCHAR(100) DEFAULT 'Comprar',
          buy_button_emoji VARCHAR(100) DEFAULT '🛒',
          buy_button_color VARCHAR(20) DEFAULT 'Primary',
          cargo_id VARCHAR(20),
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          UNIQUE(user_id, product_id)
        );
      `
    },
    {
      name: 'product_stock',
      sql: `
        CREATE TABLE IF NOT EXISTS product_stock (
          id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
          product_uuid UUID REFERENCES user_products(id) ON DELETE CASCADE,
          user_id UUID REFERENCES user_configurations(id) ON DELETE CASCADE,
          content TEXT NOT NULL,
          is_used BOOLEAN DEFAULT false,
          used_by VARCHAR(20),
          used_at TIMESTAMP WITH TIME ZONE,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
      `
    },
    {
      name: 'welcome_buttons',
      sql: `
        CREATE TABLE IF NOT EXISTS welcome_buttons (
          id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
          user_id UUID REFERENCES user_configurations(id) ON DELETE CASCADE,
          button_id VARCHAR(50) NOT NULL,
          name VARCHAR(100) NOT NULL,
          emoji VARCHAR(100),
          type VARCHAR(20) NOT NULL,
          url TEXT,
          role_id VARCHAR(20),
          channel_id VARCHAR(20),
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          UNIQUE(user_id, button_id)
        );
      `
    },
    {
      name: 'ticket_functions',
      sql: `
        CREATE TABLE IF NOT EXISTS ticket_functions (
          id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
          user_id UUID REFERENCES user_configurations(id) ON DELETE CASCADE,
          function_id VARCHAR(50) NOT NULL,
          name VARCHAR(100) NOT NULL,
          emoji VARCHAR(100) DEFAULT '🎫',
          pre_description TEXT NOT NULL,
          description TEXT,
          banner TEXT,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          UNIQUE(user_id, function_id)
        );
      `
    },
    {
      name: 'user_sales',
      sql: `
        CREATE TABLE IF NOT EXISTS user_sales (
          id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
          user_id UUID REFERENCES user_configurations(id) ON DELETE CASCADE,
          product_uuid UUID REFERENCES user_products(id),
          buyer_discord_id VARCHAR(20) NOT NULL,
          buyer_username VARCHAR(100),
          quantity INTEGER DEFAULT 1,
          total_price DECIMAL(10,2) NOT NULL,
          payment_method VARCHAR(50),
          payment_id VARCHAR(100),
          status VARCHAR(20) DEFAULT 'pending',
          items_delivered TEXT[],
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          completed_at TIMESTAMP WITH TIME ZONE
        );
      `
    }
  ];

  for (const table of tables) {
    try {
      console.log(`📝 Criando tabela ${table.name}...`);
      const { error } = await supabase.rpc('exec_sql', { sql: table.sql });
      
      if (error) {
        console.error(`Erro ao criar tabela ${table.name}:`, error);
      } else {
        console.log(`✅ Tabela ${table.name} criada com sucesso!`);
      }
    } catch (error) {
      console.error(`Erro ao processar tabela ${table.name}:`, error);
    }
  }

  // Criar índices
  const indexes = [
    'CREATE INDEX IF NOT EXISTS idx_user_configurations_discord_id ON user_configurations(discord_id);',
    'CREATE INDEX IF NOT EXISTS idx_user_categories_user_id ON user_categories(user_id);',
    'CREATE INDEX IF NOT EXISTS idx_user_products_user_id ON user_products(user_id);',
    'CREATE INDEX IF NOT EXISTS idx_product_stock_product ON product_stock(product_uuid);',
    'CREATE INDEX IF NOT EXISTS idx_user_sales_user_id ON user_sales(user_id);'
  ];

  for (const indexSql of indexes) {
    try {
      await supabase.rpc('exec_sql', { sql: indexSql });
    } catch (error) {
      console.error('Erro ao criar índice:', error);
    }
  }

  console.log('✅ Índices criados!');
}

// Executar migração
runMigration();
