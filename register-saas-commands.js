const { REST, Routes, SlashCommandBuilder } = require('discord.js');
require('dotenv').config();

// Comandos para o bot básico
const basicCommands = [
    new SlashCommandBuilder()
        .setName('config')
        .setDescription('Abrir painel de configuração do bot'),
    
    new SlashCommandBuilder()
        .setName('help')
        .setDescription('Mostrar comandos disponíveis'),
    
    new SlashCommandBuilder()
        .setName('status')
        .setDescription('Ver status do bot e licença'),
    
    new SlashCommandBuilder()
        .setName('produto')
        .setDescription('Gerenciar produtos')
        .addSubcommand(subcommand =>
            subcommand
                .setName('criar')
                .setDescription('Criar um novo produto')
                .addStringOption(option =>
                    option.setName('nome')
                        .setDescription('Nome do produto')
                        .setRequired(true))
                .addNumberOption(option =>
                    option.setName('preco')
                        .setDescription('Preço do produto')
                        .setRequired(true))
                .addStringOption(option =>
                    option.setName('categoria')
                        .setDescription('Categoria do produto')
                        .setRequired(true)))
        .addSubcommand(subcommand =>
            subcommand
                .setName('listar')
                .setDescription('Listar todos os produtos'))
        .addSubcommand(subcommand =>
            subcommand
                .setName('editar')
                .setDescription('Editar um produto existente')
                .addStringOption(option =>
                    option.setName('id')
                        .setDescription('ID do produto')
                        .setRequired(true)))
        .addSubcommand(subcommand =>
            subcommand
                .setName('deletar')
                .setDescription('Deletar um produto')
                .addStringOption(option =>
                    option.setName('id')
                        .setDescription('ID do produto')
                        .setRequired(true))),
    
    new SlashCommandBuilder()
        .setName('estoque')
        .setDescription('Gerenciar estoque de produtos')
        .addSubcommand(subcommand =>
            subcommand
                .setName('adicionar')
                .setDescription('Adicionar itens ao estoque')
                .addStringOption(option =>
                    option.setName('produto')
                        .setDescription('ID do produto')
                        .setRequired(true))
                .addStringOption(option =>
                    option.setName('itens')
                        .setDescription('Itens para adicionar (um por linha)')
                        .setRequired(true)))
        .addSubcommand(subcommand =>
            subcommand
                .setName('ver')
                .setDescription('Ver estoque de um produto')
                .addStringOption(option =>
                    option.setName('produto')
                        .setDescription('ID do produto')
                        .setRequired(true)))
        .addSubcommand(subcommand =>
            subcommand
                .setName('limpar')
                .setDescription('Limpar estoque de um produto')
                .addStringOption(option =>
                    option.setName('produto')
                        .setDescription('ID do produto')
                        .setRequired(true))),
    
    new SlashCommandBuilder()
        .setName('ticket')
        .setDescription('Gerenciar sistema de tickets')
        .addSubcommand(subcommand =>
            subcommand
                .setName('config')
                .setDescription('Configurar sistema de tickets'))
        .addSubcommand(subcommand =>
            subcommand
                .setName('postar')
                .setDescription('Postar mensagem de tickets'))
];

// Comandos para o bot pro (adicionar mais recursos)
const proCommands = [
    ...basicCommands, // Incluir todos os comandos básicos
    
    new SlashCommandBuilder()
        .setName('relatorio')
        .setDescription('Gerar relatórios de vendas')
        .addSubcommand(subcommand =>
            subcommand
                .setName('vendas')
                .setDescription('Relatório de vendas')
                .addStringOption(option =>
                    option.setName('periodo')
                        .setDescription('Período do relatório')
                        .setRequired(true)
                        .addChoices(
                            { name: 'Hoje', value: 'today' },
                            { name: 'Esta semana', value: 'week' },
                            { name: 'Este mês', value: 'month' },
                            { name: 'Personalizado', value: 'custom' }
                        )))
        .addSubcommand(subcommand =>
            subcommand
                .setName('produtos')
                .setDescription('Relatório de produtos mais vendidos'))
        .addSubcommand(subcommand =>
            subcommand
                .setName('clientes')
                .setDescription('Relatório de clientes')),
    
    new SlashCommandBuilder()
        .setName('automacao')
        .setDescription('Configurar automações avançadas')
        .addSubcommand(subcommand =>
            subcommand
                .setName('backup')
                .setDescription('Configurar backup automático'))
        .addSubcommand(subcommand =>
            subcommand
                .setName('notificacoes')
                .setDescription('Configurar notificações automáticas'))
];

async function registerCommands() {
    try {
        console.log('🔄 Registrando comandos dos bots SaaS...');

        const rest = new REST({ version: '10' });

        // Registrar comandos do bot básico
        if (process.env.BASIC_BOT_TOKEN && process.env.BASIC_BOT_CLIENT_ID) {
            console.log('📝 Registrando comandos do bot básico...');
            rest.setToken(process.env.BASIC_BOT_TOKEN);
            
            await rest.put(
                Routes.applicationCommands(process.env.BASIC_BOT_CLIENT_ID),
                { body: basicCommands.map(cmd => cmd.toJSON()) }
            );
            
            console.log('✅ Comandos do bot básico registrados com sucesso!');
        }

        // Registrar comandos do bot pro
        if (process.env.PRO_BOT_TOKEN && process.env.PRO_BOT_CLIENT_ID) {
            console.log('📝 Registrando comandos do bot pro...');
            rest.setToken(process.env.PRO_BOT_TOKEN);
            
            await rest.put(
                Routes.applicationCommands(process.env.PRO_BOT_CLIENT_ID),
                { body: proCommands.map(cmd => cmd.toJSON()) }
            );
            
            console.log('✅ Comandos do bot pro registrados com sucesso!');
        }

        console.log('🎉 Todos os comandos foram registrados!');
    } catch (error) {
        console.error('❌ Erro ao registrar comandos:', error);
    }
}

// Executar se chamado diretamente
if (require.main === module) {
    registerCommands();
}

module.exports = { registerCommands, basicCommands, proCommands };
