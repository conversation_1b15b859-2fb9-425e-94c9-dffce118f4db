-- Adici<PERSON>r colunas necessárias para o sistema de boas-vindas
-- na tabela discord_users

-- Adicionar coluna registered (se não existir)
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'discord_users' AND column_name = 'registered') THEN
        ALTER TABLE discord_users ADD COLUMN registered BOOLEAN DEFAULT FALSE;
    END IF;
END $$;

-- Adicionar coluna guild_id (se não existir)
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'discord_users' AND column_name = 'guild_id') THEN
        ALTER TABLE discord_users ADD COLUMN guild_id VARCHAR(50);
    END IF;
END $$;

-- Atualizar o usuário existente com os dados corretos
UPDATE discord_users 
SET registered = TRUE, guild_id = '1336773875543048193'
WHERE discord_id = '1202721227999944777';

-- Verificar se as colunas foram adicionadas
SELECT column_name, data_type, is_nullable 
FROM information_schema.columns 
WHERE table_name = 'discord_users' 
AND column_name IN ('registered', 'guild_id')
ORDER BY column_name;
