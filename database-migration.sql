-- Migração para Sistema SaaS - Nodex Bot
-- Este script cria as tabelas necessárias para o novo sistema centralizado

-- Tabela de usuários do sistema (substitui o sistema de arquivos local)
CREATE TABLE IF NOT EXISTS user_configurations (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    discord_id VARCHAR(20) UNIQUE NOT NULL,
    discord_username VARCHAR(100) NOT NULL,
    discord_discriminator VARCHAR(10),
    discord_avatar VARCHAR(100),
    email VARCHAR(255),
    
    -- Informações da licença
    license_key VARCHAR(30) REFERENCES licenses(key),
    license_plan VARCHAR(20) NOT NULL DEFAULT 'basic',
    license_activated_at TIMESTAMP WITH TIME ZONE,
    license_expires_at TIMESTAMP WITH TIME ZONE,
    license_validated BOOLEAN DEFAULT false,
    
    -- Configurações do bot
    guild_id VARCHAR(20),
    guild_name VARCHAR(100),
    
    -- Configurações de pagamento
    mercado_pago_access_token TEXT,
    mercado_pago_client_secret TEXT,
    mercado_pago_enabled BOOLEAN DEFAULT false,
    pix_manual_enabled BOOLEAN DEFAULT false,
    pix_manual_key TEXT,
    
    -- Configurações de logs
    logs_vendas VARCHAR(20),
    logs_carrinho VARCHAR(20),
    logs_entrada VARCHAR(20),
    logs_saida VARCHAR(20),
    logs_tickets VARCHAR(20),
    logs_pagamentos VARCHAR(20),
    logs_erros VARCHAR(20),
    logs_moderacao VARCHAR(20),
    
    -- Configurações de cargos
    cargo_dono VARCHAR(20),
    cargo_cliente VARCHAR(20),
    cargo_verificado VARCHAR(20),
    cargo_suporte VARCHAR(20),
    cargo_moderador VARCHAR(20),
    cargo_vip VARCHAR(20),
    
    -- Configurações de boas-vindas
    boas_vindas_ativo BOOLEAN DEFAULT false,
    boas_vindas_canal VARCHAR(20),
    boas_vindas_titulo TEXT,
    boas_vindas_descricao TEXT,
    boas_vindas_banner TEXT,
    boas_vindas_cor VARCHAR(7) DEFAULT '#00ff00',
    
    -- Configurações de tickets
    tickets_ativo BOOLEAN DEFAULT false,
    tickets_categoria VARCHAR(20),
    tickets_titulo TEXT DEFAULT 'Sistema de Tickets',
    tickets_descricao TEXT DEFAULT 'Clique no botão abaixo para abrir um ticket.',
    tickets_banner TEXT,
    tickets_cor VARCHAR(7) DEFAULT '#5865F2',
    
    -- Metadados
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_activity TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Tabela de categorias de produtos
CREATE TABLE IF NOT EXISTS user_categories (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES user_configurations(id) ON DELETE CASCADE,
    category_id VARCHAR(50) NOT NULL, -- ID interno da categoria
    name VARCHAR(100) NOT NULL,
    discord_category_id VARCHAR(20), -- ID da categoria no Discord
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    UNIQUE(user_id, category_id)
);

-- Tabela de produtos
CREATE TABLE IF NOT EXISTS user_products (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES user_configurations(id) ON DELETE CASCADE,
    product_id VARCHAR(50) NOT NULL, -- ID interno do produto
    category_id VARCHAR(50) NOT NULL,
    name VARCHAR(200) NOT NULL,
    description TEXT,
    price DECIMAL(10,2) DEFAULT 0,
    stock INTEGER DEFAULT 0,
    image_url TEXT,
    banner_url TEXT,
    thumbnail_url TEXT,
    color VARCHAR(7) DEFAULT '#0099ff',
    channel_id VARCHAR(20), -- Canal do produto no Discord
    embed_message_id VARCHAR(20),
    buy_button_text VARCHAR(100) DEFAULT 'Comprar',
    buy_button_emoji VARCHAR(100) DEFAULT '🛒',
    buy_button_color VARCHAR(20) DEFAULT 'Primary',
    cargo_id VARCHAR(20), -- Cargo dado após compra
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    UNIQUE(user_id, product_id)
);

-- Tabela de estoque de produtos (itens individuais)
CREATE TABLE IF NOT EXISTS product_stock (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    product_uuid UUID REFERENCES user_products(id) ON DELETE CASCADE,
    user_id UUID REFERENCES user_configurations(id) ON DELETE CASCADE,
    content TEXT NOT NULL, -- Conteúdo do item (conta, código, etc.)
    is_used BOOLEAN DEFAULT false,
    used_by VARCHAR(20), -- Discord ID de quem usou
    used_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Tabela de botões de boas-vindas
CREATE TABLE IF NOT EXISTS welcome_buttons (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES user_configurations(id) ON DELETE CASCADE,
    button_id VARCHAR(50) NOT NULL,
    name VARCHAR(100) NOT NULL,
    emoji VARCHAR(100),
    type VARCHAR(20) NOT NULL, -- 'link', 'cargo', 'canal'
    url TEXT, -- Para tipo 'link'
    role_id VARCHAR(20), -- Para tipo 'cargo'
    channel_id VARCHAR(20), -- Para tipo 'canal'
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    UNIQUE(user_id, button_id)
);

-- Tabela de funções de tickets
CREATE TABLE IF NOT EXISTS ticket_functions (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES user_configurations(id) ON DELETE CASCADE,
    function_id VARCHAR(50) NOT NULL,
    name VARCHAR(100) NOT NULL,
    emoji VARCHAR(100) DEFAULT '🎫',
    pre_description TEXT NOT NULL,
    description TEXT,
    banner TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    UNIQUE(user_id, function_id)
);

-- Tabela de cargos personalizados
CREATE TABLE IF NOT EXISTS custom_roles (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES user_configurations(id) ON DELETE CASCADE,
    role_id VARCHAR(50) NOT NULL,
    name VARCHAR(100) NOT NULL,
    discord_role_id VARCHAR(20) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    UNIQUE(user_id, role_id)
);

-- Tabela de vendas/transações
CREATE TABLE IF NOT EXISTS user_sales (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES user_configurations(id) ON DELETE CASCADE,
    product_uuid UUID REFERENCES user_products(id),
    buyer_discord_id VARCHAR(20) NOT NULL,
    buyer_username VARCHAR(100),
    quantity INTEGER DEFAULT 1,
    total_price DECIMAL(10,2) NOT NULL,
    payment_method VARCHAR(50),
    payment_id VARCHAR(100),
    status VARCHAR(20) DEFAULT 'pending', -- pending, completed, cancelled, refunded
    items_delivered TEXT[], -- Array de itens entregues
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    completed_at TIMESTAMP WITH TIME ZONE
);

-- Índices para melhor performance
CREATE INDEX IF NOT EXISTS idx_user_configurations_discord_id ON user_configurations(discord_id);
CREATE INDEX IF NOT EXISTS idx_user_configurations_license_key ON user_configurations(license_key);
CREATE INDEX IF NOT EXISTS idx_user_categories_user_id ON user_categories(user_id);
CREATE INDEX IF NOT EXISTS idx_user_products_user_id ON user_products(user_id);
CREATE INDEX IF NOT EXISTS idx_user_products_category ON user_products(user_id, category_id);
CREATE INDEX IF NOT EXISTS idx_product_stock_product ON product_stock(product_uuid);
CREATE INDEX IF NOT EXISTS idx_product_stock_available ON product_stock(product_uuid, is_used);
CREATE INDEX IF NOT EXISTS idx_welcome_buttons_user_id ON welcome_buttons(user_id);
CREATE INDEX IF NOT EXISTS idx_ticket_functions_user_id ON ticket_functions(user_id);
CREATE INDEX IF NOT EXISTS idx_custom_roles_user_id ON custom_roles(user_id);
CREATE INDEX IF NOT EXISTS idx_user_sales_user_id ON user_sales(user_id);
CREATE INDEX IF NOT EXISTS idx_user_sales_buyer ON user_sales(buyer_discord_id);

-- Trigger para atualizar updated_at automaticamente
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_user_configurations_updated_at BEFORE UPDATE ON user_configurations FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_user_products_updated_at BEFORE UPDATE ON user_products FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
