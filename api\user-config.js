import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_ANON_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

export default async function handler(req, res) {
  // Configurar CORS
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization, x-session-token');

  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }

  try {
    const { method } = req;
    const sessionToken = req.headers['x-session-token'];

    if (!sessionToken) {
      return res.status(401).json({ error: 'Token de sessão necessário' });
    }

    // Verificar autenticação (você pode implementar sua lógica de verificação aqui)
    // Por enquanto, vamos assumir que o token é válido

    switch (method) {
      case 'GET':
        return await handleGet(req, res);
      case 'POST':
        return await handlePost(req, res);
      case 'PUT':
        return await handlePut(req, res);
      case 'DELETE':
        return await handleDelete(req, res);
      default:
        return res.status(405).json({ error: 'Método não permitido' });
    }
  } catch (error) {
    console.error('Erro na API user-config:', error);
    return res.status(500).json({ error: 'Erro interno do servidor' });
  }
}

// GET /api/user-config/[discordId] - Buscar configuração do usuário
async function handleGet(req, res) {
  const { discordId } = req.query;

  if (!discordId) {
    return res.status(400).json({ error: 'Discord ID é obrigatório' });
  }

  try {
    const { data, error } = await supabase
      .from('user_configurations')
      .select('*')
      .eq('discord_id', discordId)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        return res.status(404).json({ error: 'Configuração não encontrada' });
      }
      throw error;
    }

    return res.status(200).json(data);
  } catch (error) {
    console.error('Erro ao buscar configuração:', error);
    return res.status(500).json({ error: 'Erro ao buscar configuração' });
  }
}

// POST /api/user-config - Criar nova configuração
async function handlePost(req, res) {
  const {
    discord_id,
    discord_username,
    discord_discriminator,
    discord_avatar,
    email,
    license_key,
    license_plan,
    guild_id,
    guild_name
  } = req.body;

  if (!discord_id || !discord_username) {
    return res.status(400).json({ error: 'Discord ID e username são obrigatórios' });
  }

  try {
    // Verificar se já existe configuração para este usuário
    const { data: existing } = await supabase
      .from('user_configurations')
      .select('id')
      .eq('discord_id', discord_id)
      .single();

    if (existing) {
      return res.status(409).json({ error: 'Configuração já existe para este usuário' });
    }

    // Criar nova configuração
    const { data, error } = await supabase
      .from('user_configurations')
      .insert([{
        discord_id,
        discord_username,
        discord_discriminator,
        discord_avatar,
        email,
        license_key,
        license_plan: license_plan || 'basic',
        license_validated: false,
        guild_id,
        guild_name,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        last_activity: new Date().toISOString()
      }])
      .select()
      .single();

    if (error) {
      throw error;
    }

    return res.status(201).json(data);
  } catch (error) {
    console.error('Erro ao criar configuração:', error);
    return res.status(500).json({ error: 'Erro ao criar configuração' });
  }
}

// PUT /api/user-config/[discordId] - Atualizar configuração
async function handlePut(req, res) {
  const { discordId } = req.query;
  const updateData = req.body;

  if (!discordId) {
    return res.status(400).json({ error: 'Discord ID é obrigatório' });
  }

  try {
    // Adicionar timestamp de atualização
    updateData.updated_at = new Date().toISOString();
    updateData.last_activity = new Date().toISOString();

    const { data, error } = await supabase
      .from('user_configurations')
      .update(updateData)
      .eq('discord_id', discordId)
      .select()
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        return res.status(404).json({ error: 'Configuração não encontrada' });
      }
      throw error;
    }

    return res.status(200).json(data);
  } catch (error) {
    console.error('Erro ao atualizar configuração:', error);
    return res.status(500).json({ error: 'Erro ao atualizar configuração' });
  }
}

// DELETE /api/user-config/[discordId] - Deletar configuração
async function handleDelete(req, res) {
  const { discordId } = req.query;

  if (!discordId) {
    return res.status(400).json({ error: 'Discord ID é obrigatório' });
  }

  try {
    const { error } = await supabase
      .from('user_configurations')
      .delete()
      .eq('discord_id', discordId);

    if (error) {
      throw error;
    }

    return res.status(200).json({ message: 'Configuração deletada com sucesso' });
  } catch (error) {
    console.error('Erro ao deletar configuração:', error);
    return res.status(500).json({ error: 'Erro ao deletar configuração' });
  }
}
